package com.learningplatform.payment.dto;

import java.math.BigDecimal;

/**
 * 支付请求DTO
 */
public class PaymentRequest {
    
    /**
     * 课程ID
     */
    private Long courseId;
    
    /**
     * 支付金额
     */
    private BigDecimal amount;
    
    /**
     * 支付方式：ALIPAY, WECHAT, CREDIT_CARD
     */
    private String paymentMethod;
    
    /**
     * 支付描述
     */
    private String description;
    
    // 构造函数
    public PaymentRequest() {}
    
    public PaymentRequest(Long courseId, BigDecimal amount, String paymentMethod, String description) {
        this.courseId = courseId;
        this.amount = amount;
        this.paymentMethod = paymentMethod;
        this.description = description;
    }
    
    // Getter和Setter方法
    public Long getCourseId() {
        return courseId;
    }
    
    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }
    
    public BigDecimal getAmount() {
        return amount;
    }
    
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
    
    public String getPaymentMethod() {
        return paymentMethod;
    }
    
    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    @Override
    public String toString() {
        return "PaymentRequest{" +
                "courseId=" + courseId +
                ", amount=" + amount +
                ", paymentMethod='" + paymentMethod + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
