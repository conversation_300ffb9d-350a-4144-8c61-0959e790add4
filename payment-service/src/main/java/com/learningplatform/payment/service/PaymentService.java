package com.learningplatform.payment.service;

import com.learningplatform.payment.dto.PaymentRequest;
import com.learningplatform.payment.dto.PaymentResponse;

import java.util.List;

/**
 * 支付服务接口
 */
public interface PaymentService {
    
    /**
     * 创建支付订单
     */
    PaymentResponse createPayment(Long userId, PaymentRequest request);
    
    /**
     * 模拟支付处理
     */
    PaymentResponse simulatePayment(Long paymentId);
    
    /**
     * 根据支付ID获取支付信息
     */
    PaymentResponse getPaymentById(Long paymentId);
    
    /**
     * 根据用户ID获取支付历史
     */
    List<PaymentResponse> getPaymentsByUserId(Long userId);
    
    /**
     * 根据课程ID获取支付记录
     */
    List<PaymentResponse> getPaymentsByCourseId(Long courseId);
    
    /**
     * 检查用户是否已经支付过某个课程
     */
    boolean hasUserPaidForCourse(Long userId, Long courseId);
    
    /**
     * 根据交易流水号获取支付信息
     */
    PaymentResponse getPaymentByTransactionId(String transactionId);
}
