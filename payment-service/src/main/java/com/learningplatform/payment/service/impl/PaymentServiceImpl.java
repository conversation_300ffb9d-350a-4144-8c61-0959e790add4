package com.learningplatform.payment.service.impl;

import com.learningplatform.payment.dto.PaymentRequest;
import com.learningplatform.payment.dto.PaymentResponse;
import com.learningplatform.payment.entity.Payment;
import com.learningplatform.payment.mapper.PaymentMapper;
import com.learningplatform.payment.service.PaymentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 支付服务实现类
 */
@Service
public class PaymentServiceImpl implements PaymentService {
    
    private static final Logger log = LoggerFactory.getLogger(PaymentServiceImpl.class);
    
    private final PaymentMapper paymentMapper;
    private final Random random = new Random();
    
    public PaymentServiceImpl(PaymentMapper paymentMapper) {
        this.paymentMapper = paymentMapper;
    }
    
    @Override
    @Transactional
    public PaymentResponse createPayment(Long userId, PaymentRequest request) {
        log.info("创建支付订单，用户ID: {}, 请求: {}", userId, request);
        
        // 检查用户是否已经支付过该课程
        if (hasUserPaidForCourse(userId, request.getCourseId())) {
            throw new RuntimeException("您已经购买过该课程");
        }
        
        // 创建支付记录
        Payment payment = new Payment(
            userId,
            request.getCourseId(),
            request.getAmount(),
            request.getPaymentMethod(),
            request.getDescription()
        );
        
        // 生成交易流水号
        payment.setTransactionId(generateTransactionId());
        
        paymentMapper.insert(payment);
        log.info("支付记录插入成功，支付ID: {}", payment.getId());

        PaymentResponse response = convertToResponse(payment);
        log.info("转换响应成功: {}", response);
        return response;
    }
    
    @Override
    @Transactional
    public PaymentResponse simulatePayment(Long paymentId) {
        log.info("模拟支付处理，支付ID: {}", paymentId);
        
        Payment payment = paymentMapper.selectById(paymentId);
        if (payment == null) {
            throw new RuntimeException("支付记录不存在");
        }
        
        if (!"PENDING".equals(payment.getStatus())) {
            throw new RuntimeException("支付状态不正确，当前状态: " + payment.getStatus());
        }
        
        // 模拟支付处理 - 80%成功率
        boolean success = random.nextDouble() < 0.8;
        
        if (success) {
            payment.setStatus("SUCCESS");
            log.info("支付成功，支付ID: {}", paymentId);
        } else {
            payment.setStatus("FAILED");
            log.info("支付失败，支付ID: {}", paymentId);
        }
        
        payment.setUpdatedAt(LocalDateTime.now());
        paymentMapper.updateById(payment);
        
        return convertToResponse(payment);
    }
    
    @Override
    public PaymentResponse getPaymentById(Long paymentId) {
        Payment payment = paymentMapper.selectById(paymentId);
        if (payment == null) {
            throw new RuntimeException("支付记录不存在");
        }
        return convertToResponse(payment);
    }
    
    @Override
    public List<PaymentResponse> getPaymentsByUserId(Long userId) {
        List<Payment> payments = paymentMapper.selectByUserId(userId);
        return payments.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<PaymentResponse> getPaymentsByCourseId(Long courseId) {
        List<Payment> payments = paymentMapper.selectByCourseId(courseId);
        return payments.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    @Override
    public boolean hasUserPaidForCourse(Long userId, Long courseId) {
        return paymentMapper.checkUserPaidForCourse(userId, courseId) > 0;
    }
    
    @Override
    public PaymentResponse getPaymentByTransactionId(String transactionId) {
        Payment payment = paymentMapper.selectByTransactionId(transactionId);
        if (payment == null) {
            throw new RuntimeException("支付记录不存在");
        }
        return convertToResponse(payment);
    }
    
    /**
     * 生成交易流水号
     */
    private String generateTransactionId() {
        return "PAY_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
    
    /**
     * 转换为响应DTO
     */
    private PaymentResponse convertToResponse(Payment payment) {
        PaymentResponse response = new PaymentResponse();
        BeanUtils.copyProperties(payment, response);
        return response;
    }
}
