package com.learningplatform.payment.controller;

import com.learningplatform.common.response.Result;
import com.learningplatform.payment.dto.PaymentRequest;
import com.learningplatform.payment.dto.PaymentResponse;
import com.learningplatform.payment.service.PaymentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 支付控制器
 */
@RestController
@RequestMapping("/api/payment")
public class PaymentController {
    
    private static final Logger log = LoggerFactory.getLogger(PaymentController.class);
    
    private final PaymentService paymentService;
    
    public PaymentController(PaymentService paymentService) {
        this.paymentService = paymentService;
    }
    
    /**
     * 创建支付订单
     */
    @PostMapping("/create")
    public String createPayment(
            @RequestHeader(value = "X-User-Id", required = false) String userIdHeader,
            @RequestBody PaymentRequest request,
            HttpServletResponse response) {
        try {
            log.info("创建支付订单，用户ID: {}, 请求: {}", userIdHeader, request);

            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");

            if (userIdHeader == null || userIdHeader.trim().isEmpty()) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return "{\"success\":false,\"code\":\"401\",\"message\":\"用户未登录\",\"data\":null}";
            }

            Long userId = Long.parseLong(userIdHeader);
            PaymentResponse paymentResponse = paymentService.createPayment(userId, request);
            log.info("支付服务返回响应: {}", paymentResponse);

            // 简单的成功响应
            String jsonResponse = "{\"success\":true,\"code\":\"200\",\"message\":\"支付订单创建成功\",\"data\":{\"id\":" + paymentResponse.getId() + "}}";

            log.info("准备返回JSON: {}", jsonResponse);
            response.setStatus(HttpServletResponse.SC_OK);
            return jsonResponse;
        } catch (Exception e) {
            log.error("创建支付订单失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return String.format(
                "{\"success\":false,\"code\":\"500\",\"message\":\"%s\",\"data\":null}",
                e.getMessage()
            );
        }
    }

    /**
     * 测试端点
     */
    @GetMapping("/test")
    public ResponseEntity<String> test() {
        return ResponseEntity.ok("Payment service is working!");
    }

    /**
     * 简单创建测试端点
     */
    @PostMapping("/create-simple")
    public ResponseEntity<String> createPaymentSimple() {
        return ResponseEntity.ok("{\"success\":true,\"message\":\"简单测试成功\"}");
    }

    /**
     * 模拟支付处理
     */
    @PostMapping("/simulate")
    public ResponseEntity<Result<PaymentResponse>> simulatePayment(@RequestParam Long paymentId) {
        try {
            log.info("模拟支付处理，支付ID: {}", paymentId);
            
            PaymentResponse response = paymentService.simulatePayment(paymentId);
            
            String message = "SUCCESS".equals(response.getStatus()) ? "支付成功" : "支付失败";
            Result<PaymentResponse> result = Result.success(response);
            result.setMessage(message);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("模拟支付处理失败", e);
            return ResponseEntity.badRequest().body(Result.error(e.getMessage()));
        }
    }
    
    /**
     * 获取支付信息
     */
    @GetMapping("/{paymentId}")
    public ResponseEntity<Result<PaymentResponse>> getPayment(@PathVariable Long paymentId) {
        try {
            PaymentResponse response = paymentService.getPaymentById(paymentId);
            return ResponseEntity.ok(Result.success(response));
        } catch (Exception e) {
            log.error("获取支付信息失败", e);
            return ResponseEntity.badRequest().body(Result.error(e.getMessage()));
        }
    }
    
    /**
     * 获取用户支付历史
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<Result<List<PaymentResponse>>> getUserPayments(@PathVariable Long userId) {
        try {
            List<PaymentResponse> responses = paymentService.getPaymentsByUserId(userId);
            return ResponseEntity.ok(Result.success(responses));
        } catch (Exception e) {
            log.error("获取用户支付历史失败", e);
            return ResponseEntity.badRequest().body(Result.error(e.getMessage()));
        }
    }
    
    /**
     * 获取课程支付记录
     */
    @GetMapping("/course/{courseId}")
    public ResponseEntity<Result<List<PaymentResponse>>> getCoursePayments(@PathVariable Long courseId) {
        try {
            List<PaymentResponse> responses = paymentService.getPaymentsByCourseId(courseId);
            return ResponseEntity.ok(Result.success(responses));
        } catch (Exception e) {
            log.error("获取课程支付记录失败", e);
            return ResponseEntity.badRequest().body(Result.error(e.getMessage()));
        }
    }
    
    /**
     * 检查用户是否已支付课程
     */
    @GetMapping("/check")
    public ResponseEntity<Result<Boolean>> checkUserPaidForCourse(
            @RequestParam Long userId,
            @RequestParam Long courseId) {
        try {
            boolean paid = paymentService.hasUserPaidForCourse(userId, courseId);
            return ResponseEntity.ok(Result.success(paid));
        } catch (Exception e) {
            log.error("检查用户支付状态失败", e);
            return ResponseEntity.badRequest().body(Result.error(e.getMessage()));
        }
    }
    
    /**
     * 根据交易流水号获取支付信息
     */
    @GetMapping("/transaction/{transactionId}")
    public ResponseEntity<Result<PaymentResponse>> getPaymentByTransactionId(@PathVariable String transactionId) {
        try {
            PaymentResponse response = paymentService.getPaymentByTransactionId(transactionId);
            return ResponseEntity.ok(Result.success(response));
        } catch (Exception e) {
            log.error("根据交易流水号获取支付信息失败", e);
            return ResponseEntity.badRequest().body(Result.error(e.getMessage()));
        }
    }
}
