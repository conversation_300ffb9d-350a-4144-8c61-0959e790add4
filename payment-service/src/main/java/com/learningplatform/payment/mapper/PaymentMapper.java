package com.learningplatform.payment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.learningplatform.payment.entity.Payment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 支付记录Mapper接口
 */
@Mapper
public interface PaymentMapper extends BaseMapper<Payment> {
    
    /**
     * 根据用户ID获取支付记录
     */
    @Select("SELECT * FROM payments WHERE user_id = #{userId} AND deleted = 0 ORDER BY created_at DESC")
    List<Payment> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据课程ID获取支付记录
     */
    @Select("SELECT * FROM payments WHERE course_id = #{courseId} AND deleted = 0 ORDER BY created_at DESC")
    List<Payment> selectByCourseId(@Param("courseId") Long courseId);
    
    /**
     * 根据交易流水号获取支付记录
     */
    @Select("SELECT * FROM payments WHERE transaction_id = #{transactionId} AND deleted = 0")
    Payment selectByTransactionId(@Param("transactionId") String transactionId);
    
    /**
     * 检查用户是否已经支付过某个课程
     */
    @Select("SELECT COUNT(*) FROM payments WHERE user_id = #{userId} AND course_id = #{courseId} AND status = 'SUCCESS' AND deleted = 0")
    int checkUserPaidForCourse(@Param("userId") Long userId, @Param("courseId") Long courseId);
}
