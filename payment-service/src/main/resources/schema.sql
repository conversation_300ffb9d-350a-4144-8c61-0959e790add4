-- 创建支付记录表
CREATE TABLE IF NOT EXISTS payments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    payment_method VARCHAR(20) NOT NULL,
    transaction_id VARCHAR(100) UNIQUE,
    description VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_course_id ON payments(course_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_transaction_id ON payments(transaction_id);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

-- 插入测试数据
INSERT INTO payments (user_id, course_id, amount, status, payment_method, transaction_id, description, created_at, updated_at) VALUES
(1, 3, 199.00, 'SUCCESS', 'ALIPAY', 'PAY_1704067200000_ABC12345', '购买课程：UI/UX设计基础', '2025-01-01 10:00:00', '2025-01-01 10:05:00'),
(2, 4, 299.00, 'SUCCESS', 'WECHAT', 'PAY_1704153600000_DEF67890', '购买课程：Python数据分析', '2025-01-02 14:30:00', '2025-01-02 14:35:00'),
(3, 3, 199.00, 'FAILED', 'CREDIT_CARD', 'PAY_1704240000000_GHI11111', '购买课程：UI/UX设计基础', '2025-01-03 09:15:00', '2025-01-03 09:20:00'),
(1, 4, 299.00, 'PENDING', 'ALIPAY', 'PAY_1704326400000_JKL22222', '购买课程：Python数据分析', '2025-01-04 16:45:00', '2025-01-04 16:45:00');
