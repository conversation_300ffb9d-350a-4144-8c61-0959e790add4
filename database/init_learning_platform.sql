-- 学习平台数据库初始化脚本
-- 创建数据库和表，插入测试数据

-- 创建数据库
DROP DATABASE IF EXISTS learning_platform;
CREATE DATABASE learning_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE learning_platform;

-- 创建用户表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    nickname VARCHAR(100),
    avatar_url VARCHAR(255),
    phone VARCHAR(20),
    role ENUM('STUDENT', 'TEACHER', 'ADMIN') DEFAULT 'STUDENT',
    status ENUM('ACTIVE', 'INACTIVE', 'BANNED') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0
);

-- 创建课程表
CREATE TABLE courses (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    content TEXT,
    instructor_id BIGINT,
    category VARCHAR(100),
    difficulty_level ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED') DEFAULT 'BEGINNER',
    duration_hours INT DEFAULT 0,
    price DECIMAL(10,2) DEFAULT 0.00,
    status ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED') DEFAULT 'PUBLISHED',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_category (category),
    INDEX idx_instructor (instructor_id),
    INDEX idx_status (status)
);

-- 创建学习记录表
CREATE TABLE learning_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    progress DECIMAL(5,2) DEFAULT 0.00,
    status ENUM('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'PAUSED') DEFAULT 'NOT_STARTED',
    start_time TIMESTAMP NULL,
    completion_time TIMESTAMP NULL,
    last_access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    UNIQUE KEY uk_user_course (user_id, course_id),
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_status (status)
);

-- 创建用户行为表
CREATE TABLE user_behaviors (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    behavior_type ENUM('VIEW', 'CLICK', 'ENROLL', 'COMPLETE', 'LIKE', 'SHARE', 'COMMENT') NOT NULL,
    behavior_value DECIMAL(5,2) DEFAULT 1.0,
    session_id VARCHAR(100),
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_behavior_type (behavior_type),
    INDEX idx_created_at (created_at)
);

-- 创建推荐表
CREATE TABLE recommendations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    algorithm_type ENUM('COLLABORATIVE', 'CONTENT_BASED', 'HYBRID', 'POPULAR') NOT NULL,
    score DECIMAL(5,4) NOT NULL,
    reason VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_algorithm (algorithm_type),
    INDEX idx_score (score DESC)
);

-- 插入用户数据
INSERT INTO users (username, email, password, nickname, role, status) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '管理员', 'ADMIN', 'ACTIVE'),
('user', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '普通用户', 'STUDENT', 'ACTIVE'),
('123', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '1234', 'STUDENT', 'ACTIVE'),
('teacher1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '张老师', 'TEACHER', 'ACTIVE'),
('teacher2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '李老师', 'TEACHER', 'ACTIVE'),
('student1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '小明', 'STUDENT', 'ACTIVE'),
('student2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '小红', 'STUDENT', 'ACTIVE'),
('student3', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '小刚', 'STUDENT', 'ACTIVE'),
('student4', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '小丽', 'STUDENT', 'ACTIVE'),
('student5', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '小华', 'STUDENT', 'ACTIVE');

-- 插入课程数据
INSERT INTO courses (title, description, content, instructor_id, category, difficulty_level, duration_hours, price, status) VALUES
('Java基础编程', 'Java编程语言基础课程，适合初学者', 'Java语法、面向对象编程、集合框架等基础知识', 4, 'Programming', 'BEGINNER', 40, 199.00, 'PUBLISHED'),
('Spring Boot实战', 'Spring Boot框架实战开发课程', 'Spring Boot核心概念、自动配置、Web开发、数据访问等', 4, 'Programming', 'INTERMEDIATE', 60, 299.00, 'PUBLISHED'),
('MySQL数据库设计', 'MySQL数据库设计与优化课程', 'SQL语法、数据库设计、索引优化、性能调优', 5, 'Database', 'INTERMEDIATE', 35, 249.00, 'PUBLISHED'),
('前端开发入门', 'HTML、CSS、JavaScript基础课程', 'Web前端开发基础知识，包括HTML5、CSS3、ES6等', 4, 'Frontend', 'BEGINNER', 45, 179.00, 'PUBLISHED'),
('Vue.js框架开发', 'Vue.js前端框架开发课程', 'Vue.js组件开发、路由管理、状态管理、项目实战', 5, 'Frontend', 'INTERMEDIATE', 50, 279.00, 'PUBLISHED'),
('Python数据分析', 'Python数据分析与可视化课程', 'NumPy、Pandas、Matplotlib、数据清洗与分析', 4, 'DataScience', 'INTERMEDIATE', 55, 329.00, 'PUBLISHED'),
('机器学习基础', '机器学习算法与应用课程', '监督学习、无监督学习、深度学习基础、实际案例', 5, 'DataScience', 'ADVANCED', 80, 499.00, 'PUBLISHED'),
('Docker容器技术', 'Docker容器化部署课程', 'Docker基础、镜像制作、容器编排、微服务部署', 4, 'DevOps', 'INTERMEDIATE', 30, 219.00, 'PUBLISHED'),
('Kubernetes集群管理', 'Kubernetes容器编排课程', 'K8s集群搭建、Pod管理、服务发现、自动扩缩容', 5, 'DevOps', 'ADVANCED', 65, 399.00, 'PUBLISHED'),
('React开发实战', 'React前端框架开发课程', 'React组件、Hooks、状态管理、路由、项目实战', 4, 'Frontend', 'INTERMEDIATE', 55, 299.00, 'PUBLISHED'),
('Node.js后端开发', 'Node.js服务端开发课程', 'Express框架、API开发、数据库集成、身份认证', 5, 'Backend', 'INTERMEDIATE', 45, 259.00, 'PUBLISHED'),
('微服务架构设计', '微服务架构设计与实现课程', '服务拆分、API网关、服务发现、分布式事务', 4, 'Architecture', 'ADVANCED', 70, 449.00, 'PUBLISHED'),
('Redis缓存技术', 'Redis缓存数据库课程', 'Redis数据结构、持久化、集群、缓存策略', 5, 'Database', 'INTERMEDIATE', 25, 189.00, 'PUBLISHED'),
('Git版本控制', 'Git版本控制系统课程', 'Git基础命令、分支管理、协作开发、工作流程', 4, 'Tools', 'BEGINNER', 20, 99.00, 'PUBLISHED'),
('Linux系统管理', 'Linux系统管理与运维课程', 'Linux基础、文件系统、进程管理、网络配置', 5, 'System', 'INTERMEDIATE', 40, 229.00, 'PUBLISHED'),
('算法与数据结构', '算法与数据结构基础课程', '排序算法、查找算法、树、图、动态规划', 4, 'Algorithm', 'INTERMEDIATE', 60, 349.00, 'PUBLISHED'),
('网络安全基础', '网络安全与信息安全课程', '网络攻防、加密技术、安全协议、漏洞分析', 5, 'Security', 'INTERMEDIATE', 45, 299.00, 'PUBLISHED'),
('UI/UX设计', '用户界面与用户体验设计课程', '设计原则、原型设计、用户研究、交互设计', 4, 'Design', 'BEGINNER', 35, 199.00, 'PUBLISHED'),
('移动应用开发', 'Android/iOS移动应用开发课程', '移动端开发、跨平台框架、应用发布', 5, 'Mobile', 'INTERMEDIATE', 65, 379.00, 'PUBLISHED'),
('区块链技术', '区块链技术原理与应用课程', '区块链基础、智能合约、DApp开发、加密货币', 4, 'Blockchain', 'ADVANCED', 50, 399.00, 'PUBLISHED');

-- 插入学习记录数据
INSERT INTO learning_records (user_id, course_id, progress, status, start_time, completion_time, last_access_time) VALUES
-- admin用户的学习记录
(1, 1, 100.00, 'COMPLETED', '2025-07-01 09:00:00', '2025-07-15 18:30:00', '2025-07-15 18:30:00'),
(1, 2, 85.50, 'IN_PROGRESS', '2025-07-16 10:00:00', NULL, '2025-08-01 14:20:00'),
(1, 3, 100.00, 'COMPLETED', '2025-06-15 08:30:00', '2025-06-30 16:45:00', '2025-06-30 16:45:00'),
(1, 8, 75.20, 'IN_PROGRESS', '2025-07-20 11:00:00', NULL, '2025-08-01 09:15:00'),
(1, 12, 60.30, 'IN_PROGRESS', '2025-07-25 13:30:00', NULL, '2025-07-31 17:00:00'),

-- user用户的学习记录
(2, 1, 100.00, 'COMPLETED', '2025-06-20 10:30:00', '2025-07-05 19:15:00', '2025-07-05 19:15:00'),
(2, 4, 100.00, 'COMPLETED', '2025-07-06 09:15:00', '2025-07-25 20:30:00', '2025-07-25 20:30:00'),
(2, 5, 90.75, 'IN_PROGRESS', '2025-07-26 14:00:00', NULL, '2025-08-01 16:45:00'),
(2, 10, 45.60, 'IN_PROGRESS', '2025-07-28 11:30:00', NULL, '2025-08-01 13:20:00'),
(2, 14, 100.00, 'COMPLETED', '2025-06-01 08:00:00', '2025-06-10 17:30:00', '2025-06-10 17:30:00'),

-- 123用户的学习记录
(9, 1, 95.80, 'IN_PROGRESS', '2025-07-10 09:45:00', NULL, '2025-08-01 15:30:00'),
(9, 6, 100.00, 'COMPLETED', '2025-06-25 10:00:00', '2025-07-20 18:45:00', '2025-07-20 18:45:00'),
(9, 7, 70.25, 'IN_PROGRESS', '2025-07-21 13:15:00', NULL, '2025-08-01 11:00:00'),
(9, 16, 55.40, 'IN_PROGRESS', '2025-07-15 16:30:00', NULL, '2025-07-30 14:15:00'),

-- 其他学生的学习记录
(6, 1, 100.00, 'COMPLETED', '2025-06-10 09:00:00', '2025-06-25 17:30:00', '2025-06-25 17:30:00'),
(6, 4, 80.30, 'IN_PROGRESS', '2025-06-26 10:15:00', NULL, '2025-07-31 16:20:00'),
(6, 14, 100.00, 'COMPLETED', '2025-05-15 08:30:00', '2025-05-25 19:00:00', '2025-05-25 19:00:00'),
(7, 2, 65.75, 'IN_PROGRESS', '2025-07-05 11:00:00', NULL, '2025-08-01 12:45:00'),
(7, 3, 100.00, 'COMPLETED', '2025-06-01 09:30:00', '2025-06-20 18:15:00', '2025-06-20 18:15:00'),
(7, 15, 40.20, 'IN_PROGRESS', '2025-07-22 14:30:00', NULL, '2025-07-29 10:30:00'),
(8, 5, 100.00, 'COMPLETED', '2025-06-15 10:45:00', '2025-07-10 19:30:00', '2025-07-10 19:30:00'),
(8, 10, 85.60, 'IN_PROGRESS', '2025-07-11 13:00:00', NULL, '2025-08-01 15:45:00'),
(8, 18, 30.15, 'IN_PROGRESS', '2025-07-25 16:15:00', NULL, '2025-07-28 11:30:00'),
(10, 6, 75.45, 'IN_PROGRESS', '2025-07-08 09:15:00', NULL, '2025-08-01 14:00:00'),
(10, 7, 50.30, 'IN_PROGRESS', '2025-07-18 11:45:00', NULL, '2025-07-31 16:30:00'),
(11, 9, 60.80, 'IN_PROGRESS', '2025-07-12 10:30:00', NULL, '2025-08-01 13:15:00'),
(11, 12, 35.25, 'IN_PROGRESS', '2025-07-20 15:00:00', NULL, '2025-07-30 09:45:00');

-- 插入用户行为数据
INSERT INTO user_behaviors (user_id, course_id, behavior_type, behavior_value, created_at) VALUES
-- admin用户的行为
(1, 1, 'VIEW', 1.0, '2025-07-01 09:00:00'),
(1, 1, 'ENROLL', 3.0, '2025-07-01 09:05:00'),
(1, 1, 'COMPLETE', 5.0, '2025-07-15 18:30:00'),
(1, 1, 'LIKE', 2.0, '2025-07-15 18:35:00'),
(1, 2, 'VIEW', 1.0, '2025-07-16 10:00:00'),
(1, 2, 'ENROLL', 3.0, '2025-07-16 10:05:00'),
(1, 2, 'CLICK', 1.0, '2025-08-01 14:20:00'),
(1, 3, 'VIEW', 1.0, '2025-06-15 08:30:00'),
(1, 3, 'ENROLL', 3.0, '2025-06-15 08:35:00'),
(1, 3, 'COMPLETE', 5.0, '2025-06-30 16:45:00'),
(1, 3, 'LIKE', 2.0, '2025-06-30 16:50:00'),
(1, 8, 'VIEW', 1.0, '2025-07-20 11:00:00'),
(1, 8, 'ENROLL', 3.0, '2025-07-20 11:05:00'),
(1, 8, 'CLICK', 1.0, '2025-08-01 09:15:00'),
(1, 12, 'VIEW', 1.0, '2025-07-25 13:30:00'),
(1, 12, 'ENROLL', 3.0, '2025-07-25 13:35:00'),
(1, 12, 'CLICK', 1.0, '2025-07-31 17:00:00'),
-- admin浏览其他课程
(1, 4, 'VIEW', 1.0, '2025-07-28 10:15:00'),
(1, 5, 'VIEW', 1.0, '2025-07-28 10:20:00'),
(1, 6, 'VIEW', 1.0, '2025-07-28 10:25:00'),
(1, 7, 'VIEW', 1.0, '2025-07-29 14:30:00'),
(1, 9, 'VIEW', 1.0, '2025-07-29 14:35:00'),
(1, 10, 'VIEW', 1.0, '2025-07-30 16:40:00'),
(1, 11, 'VIEW', 1.0, '2025-07-30 16:45:00'),

-- user用户的行为
(2, 1, 'VIEW', 1.0, '2025-06-20 10:30:00'),
(2, 1, 'ENROLL', 3.0, '2025-06-20 10:35:00'),
(2, 1, 'COMPLETE', 5.0, '2025-07-05 19:15:00'),
(2, 1, 'LIKE', 2.0, '2025-07-05 19:20:00'),
(2, 4, 'VIEW', 1.0, '2025-07-06 09:15:00'),
(2, 4, 'ENROLL', 3.0, '2025-07-06 09:20:00'),
(2, 4, 'COMPLETE', 5.0, '2025-07-25 20:30:00'),
(2, 4, 'LIKE', 2.0, '2025-07-25 20:35:00'),
(2, 5, 'VIEW', 1.0, '2025-07-26 14:00:00'),
(2, 5, 'ENROLL', 3.0, '2025-07-26 14:05:00'),
(2, 5, 'CLICK', 1.0, '2025-08-01 16:45:00'),
(2, 10, 'VIEW', 1.0, '2025-07-28 11:30:00'),
(2, 10, 'ENROLL', 3.0, '2025-07-28 11:35:00'),
(2, 10, 'CLICK', 1.0, '2025-08-01 13:20:00'),
(2, 14, 'VIEW', 1.0, '2025-06-01 08:00:00'),
(2, 14, 'ENROLL', 3.0, '2025-06-01 08:05:00'),
(2, 14, 'COMPLETE', 5.0, '2025-06-10 17:30:00'),
(2, 14, 'LIKE', 2.0, '2025-06-10 17:35:00'),
-- user浏览其他课程
(2, 2, 'VIEW', 1.0, '2025-07-27 15:20:00'),
(2, 3, 'VIEW', 1.0, '2025-07-27 15:25:00'),
(2, 6, 'VIEW', 1.0, '2025-07-27 15:30:00'),
(2, 7, 'VIEW', 1.0, '2025-07-28 09:15:00'),
(2, 8, 'VIEW', 1.0, '2025-07-28 09:20:00'),
(2, 9, 'VIEW', 1.0, '2025-07-29 11:10:00'),
(2, 11, 'VIEW', 1.0, '2025-07-29 11:15:00'),
(2, 12, 'VIEW', 1.0, '2025-07-30 13:25:00'),
(2, 13, 'VIEW', 1.0, '2025-07-30 13:30:00'),
(2, 15, 'VIEW', 1.0, '2025-07-31 10:45:00'),
(2, 16, 'VIEW', 1.0, '2025-07-31 10:50:00'),
(2, 17, 'VIEW', 1.0, '2025-07-31 14:20:00'),
(2, 18, 'VIEW', 1.0, '2025-07-31 14:25:00'),

-- 123用户的行为
(9, 1, 'VIEW', 1.0, '2025-07-10 09:45:00'),
(9, 1, 'ENROLL', 3.0, '2025-07-10 09:50:00'),
(9, 1, 'CLICK', 1.0, '2025-08-01 15:30:00'),
(9, 6, 'VIEW', 1.0, '2025-06-25 10:00:00'),
(9, 6, 'ENROLL', 3.0, '2025-06-25 10:05:00'),
(9, 6, 'COMPLETE', 5.0, '2025-07-20 18:45:00'),
(9, 6, 'LIKE', 2.0, '2025-07-20 18:50:00'),
(9, 7, 'VIEW', 1.0, '2025-07-21 13:15:00'),
(9, 7, 'ENROLL', 3.0, '2025-07-21 13:20:00'),
(9, 7, 'CLICK', 1.0, '2025-08-01 11:00:00'),
(9, 16, 'VIEW', 1.0, '2025-07-15 16:30:00'),
(9, 16, 'ENROLL', 3.0, '2025-07-15 16:35:00'),
(9, 16, 'CLICK', 1.0, '2025-07-30 14:15:00'),
-- 123浏览其他课程
(9, 2, 'VIEW', 1.0, '2025-07-22 11:20:00'),
(9, 3, 'VIEW', 1.0, '2025-07-22 11:25:00'),
(9, 4, 'VIEW', 1.0, '2025-07-23 14:30:00'),
(9, 5, 'VIEW', 1.0, '2025-07-23 14:35:00'),
(9, 8, 'VIEW', 1.0, '2025-07-24 16:40:00'),
(9, 9, 'VIEW', 1.0, '2025-07-24 16:45:00'),
(9, 10, 'VIEW', 1.0, '2025-07-25 09:15:00'),
(9, 11, 'VIEW', 1.0, '2025-07-25 09:20:00'),
(9, 12, 'VIEW', 1.0, '2025-07-26 13:25:00'),
(9, 13, 'VIEW', 1.0, '2025-07-26 13:30:00'),
(9, 14, 'VIEW', 1.0, '2025-07-27 15:35:00'),
(9, 15, 'VIEW', 1.0, '2025-07-27 15:40:00'),
(9, 17, 'VIEW', 1.0, '2025-07-28 10:45:00'),
(9, 18, 'VIEW', 1.0, '2025-07-28 10:50:00'),
(9, 19, 'VIEW', 1.0, '2025-07-29 12:55:00'),
(9, 20, 'VIEW', 1.0, '2025-07-29 13:00:00'),

-- 其他学生的行为数据
(6, 1, 'VIEW', 1.0, '2025-06-10 09:00:00'),
(6, 1, 'ENROLL', 3.0, '2025-06-10 09:05:00'),
(6, 1, 'COMPLETE', 5.0, '2025-06-25 17:30:00'),
(6, 1, 'LIKE', 2.0, '2025-06-25 17:35:00'),
(6, 4, 'VIEW', 1.0, '2025-06-26 10:15:00'),
(6, 4, 'ENROLL', 3.0, '2025-06-26 10:20:00'),
(6, 4, 'CLICK', 1.0, '2025-07-31 16:20:00'),
(6, 14, 'VIEW', 1.0, '2025-05-15 08:30:00'),
(6, 14, 'ENROLL', 3.0, '2025-05-15 08:35:00'),
(6, 14, 'COMPLETE', 5.0, '2025-05-25 19:00:00'),
(6, 14, 'LIKE', 2.0, '2025-05-25 19:05:00'),
(6, 2, 'VIEW', 1.0, '2025-07-01 11:30:00'),
(6, 3, 'VIEW', 1.0, '2025-07-01 11:35:00'),
(6, 5, 'VIEW', 1.0, '2025-07-02 14:20:00'),
(6, 6, 'VIEW', 1.0, '2025-07-02 14:25:00'),
(6, 7, 'VIEW', 1.0, '2025-07-03 16:30:00'),
(6, 8, 'VIEW', 1.0, '2025-07-03 16:35:00'),

(7, 2, 'VIEW', 1.0, '2025-07-05 11:00:00'),
(7, 2, 'ENROLL', 3.0, '2025-07-05 11:05:00'),
(7, 2, 'CLICK', 1.0, '2025-08-01 12:45:00'),
(7, 3, 'VIEW', 1.0, '2025-06-01 09:30:00'),
(7, 3, 'ENROLL', 3.0, '2025-06-01 09:35:00'),
(7, 3, 'COMPLETE', 5.0, '2025-06-20 18:15:00'),
(7, 3, 'LIKE', 2.0, '2025-06-20 18:20:00'),
(7, 15, 'VIEW', 1.0, '2025-07-22 14:30:00'),
(7, 15, 'ENROLL', 3.0, '2025-07-22 14:35:00'),
(7, 15, 'CLICK', 1.0, '2025-07-29 10:30:00'),
(7, 1, 'VIEW', 1.0, '2025-07-10 13:20:00'),
(7, 4, 'VIEW', 1.0, '2025-07-10 13:25:00'),
(7, 5, 'VIEW', 1.0, '2025-07-11 15:30:00'),
(7, 6, 'VIEW', 1.0, '2025-07-11 15:35:00'),
(7, 8, 'VIEW', 1.0, '2025-07-12 17:40:00'),
(7, 9, 'VIEW', 1.0, '2025-07-12 17:45:00'),

(8, 5, 'VIEW', 1.0, '2025-06-15 10:45:00'),
(8, 5, 'ENROLL', 3.0, '2025-06-15 10:50:00'),
(8, 5, 'COMPLETE', 5.0, '2025-07-10 19:30:00'),
(8, 5, 'LIKE', 2.0, '2025-07-10 19:35:00'),
(8, 10, 'VIEW', 1.0, '2025-07-11 13:00:00'),
(8, 10, 'ENROLL', 3.0, '2025-07-11 13:05:00'),
(8, 10, 'CLICK', 1.0, '2025-08-01 15:45:00'),
(8, 18, 'VIEW', 1.0, '2025-07-25 16:15:00'),
(8, 18, 'ENROLL', 3.0, '2025-07-25 16:20:00'),
(8, 18, 'CLICK', 1.0, '2025-07-28 11:30:00'),
(8, 1, 'VIEW', 1.0, '2025-07-15 14:25:00'),
(8, 2, 'VIEW', 1.0, '2025-07-15 14:30:00'),
(8, 4, 'VIEW', 1.0, '2025-07-16 16:35:00'),
(8, 6, 'VIEW', 1.0, '2025-07-16 16:40:00'),
(8, 7, 'VIEW', 1.0, '2025-07-17 18:45:00'),
(8, 8, 'VIEW', 1.0, '2025-07-17 18:50:00'),

(10, 6, 'VIEW', 1.0, '2025-07-08 09:15:00'),
(10, 6, 'ENROLL', 3.0, '2025-07-08 09:20:00'),
(10, 6, 'CLICK', 1.0, '2025-08-01 14:00:00'),
(10, 7, 'VIEW', 1.0, '2025-07-18 11:45:00'),
(10, 7, 'ENROLL', 3.0, '2025-07-18 11:50:00'),
(10, 7, 'CLICK', 1.0, '2025-07-31 16:30:00'),
(10, 1, 'VIEW', 1.0, '2025-07-20 13:25:00'),
(10, 2, 'VIEW', 1.0, '2025-07-20 13:30:00'),
(10, 3, 'VIEW', 1.0, '2025-07-21 15:35:00'),
(10, 4, 'VIEW', 1.0, '2025-07-21 15:40:00'),
(10, 5, 'VIEW', 1.0, '2025-07-22 17:45:00'),
(10, 8, 'VIEW', 1.0, '2025-07-22 17:50:00'),

(11, 9, 'VIEW', 1.0, '2025-07-12 10:30:00'),
(11, 9, 'ENROLL', 3.0, '2025-07-12 10:35:00'),
(11, 9, 'CLICK', 1.0, '2025-08-01 13:15:00'),
(11, 12, 'VIEW', 1.0, '2025-07-20 15:00:00'),
(11, 12, 'ENROLL', 3.0, '2025-07-20 15:05:00'),
(11, 12, 'CLICK', 1.0, '2025-07-30 09:45:00'),
(11, 1, 'VIEW', 1.0, '2025-07-25 11:20:00'),
(11, 2, 'VIEW', 1.0, '2025-07-25 11:25:00'),
(11, 3, 'VIEW', 1.0, '2025-07-26 13:30:00'),
(11, 4, 'VIEW', 1.0, '2025-07-26 13:35:00'),
(11, 5, 'VIEW', 1.0, '2025-07-27 15:40:00'),
(11, 6, 'VIEW', 1.0, '2025-07-27 15:45:00'),
(11, 7, 'VIEW', 1.0, '2025-07-28 17:50:00'),
(11, 8, 'VIEW', 1.0, '2025-07-28 17:55:00');

-- 插入初始推荐数据（基于用户行为生成的推荐）
INSERT INTO recommendations (user_id, course_id, algorithm_type, score, reason) VALUES
-- admin用户的推荐
(1, 4, 'COLLABORATIVE', 0.8500, '基于相似用户的学习偏好推荐'),
(1, 5, 'COLLABORATIVE', 0.7800, '学习了Java基础的用户也喜欢前端开发'),
(1, 6, 'CONTENT_BASED', 0.9200, '基于您对编程课程的兴趣'),
(1, 7, 'HYBRID', 0.8900, '结合协同过滤和内容分析的推荐'),
(1, 9, 'COLLABORATIVE', 0.7600, '相似背景用户的热门选择'),
(1, 10, 'CONTENT_BASED', 0.8300, '与您已学课程内容相关'),
(1, 11, 'HYBRID', 0.8100, '综合推荐算法建议'),
(1, 13, 'COLLABORATIVE', 0.7400, '基于用户群体偏好'),
(1, 15, 'CONTENT_BASED', 0.7900, '技术栈相关推荐'),
(1, 16, 'HYBRID', 0.8600, '个性化混合推荐'),
(1, 17, 'COLLABORATIVE', 0.7200, '相似用户喜欢的课程'),
(1, 19, 'CONTENT_BASED', 0.7700, '基于学习历史的内容推荐'),

-- user用户的推荐
(2, 2, 'COLLABORATIVE', 0.8700, '学习了Java基础的用户推荐'),
(2, 3, 'CONTENT_BASED', 0.8400, '与前端开发相关的技术'),
(2, 6, 'HYBRID', 0.9100, '数据分析是热门发展方向'),
(2, 7, 'COLLABORATIVE', 0.8800, '相似学习路径用户的选择'),
(2, 8, 'CONTENT_BASED', 0.8000, '技术栈扩展推荐'),
(2, 9, 'HYBRID', 0.8500, '进阶技术课程推荐'),
(2, 11, 'COLLABORATIVE', 0.7900, '后端开发进阶课程'),
(2, 12, 'CONTENT_BASED', 0.8200, '架构设计相关推荐'),
(2, 13, 'HYBRID', 0.7700, '缓存技术补充学习'),
(2, 16, 'COLLABORATIVE', 0.8300, '算法基础强化推荐'),
(2, 19, 'CONTENT_BASED', 0.7500, '移动开发扩展方向'),
(2, 20, 'HYBRID', 0.7800, '新兴技术探索推荐'),

-- 123用户的推荐
(9, 2, 'COLLABORATIVE', 0.8600, '基于Java基础学习的进阶推荐'),
(9, 3, 'CONTENT_BASED', 0.8200, '数据库技术是编程必备技能'),
(9, 4, 'HYBRID', 0.7900, '前端技术栈补充'),
(9, 5, 'COLLABORATIVE', 0.8400, 'Vue.js是热门前端框架'),
(9, 8, 'CONTENT_BASED', 0.8700, '容器技术是现代开发趋势'),
(9, 9, 'HYBRID', 0.8300, 'Kubernetes进阶容器编排'),
(9, 10, 'COLLABORATIVE', 0.7800, 'React与Vue.js相似技术栈'),
(9, 11, 'CONTENT_BASED', 0.8100, 'Node.js后端开发扩展'),
(9, 12, 'HYBRID', 0.8500, '微服务架构设计进阶'),
(9, 13, 'COLLABORATIVE', 0.7600, 'Redis缓存技术实用推荐'),
(9, 14, 'CONTENT_BASED', 0.7400, 'Git版本控制基础工具'),
(9, 15, 'HYBRID', 0.7700, 'Linux系统管理技能'),
(9, 17, 'COLLABORATIVE', 0.7500, '网络安全意识培养'),
(9, 18, 'CONTENT_BASED', 0.7300, 'UI/UX设计思维拓展'),
(9, 19, 'HYBRID', 0.7900, '移动应用开发新方向'),
(9, 20, 'COLLABORATIVE', 0.8000, '区块链技术前沿探索');

-- 更新用户密码为正确的BCrypt哈希值
-- admin/admin, user/user 的密码哈希
UPDATE users SET password = '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq' WHERE username IN ('admin', 'user');

-- 显示统计信息
SELECT '数据初始化完成！' as message;
SELECT CONCAT('用户总数: ', COUNT(*)) as user_count FROM users WHERE deleted = 0;
SELECT CONCAT('课程总数: ', COUNT(*)) as course_count FROM courses WHERE deleted = 0;
SELECT CONCAT('学习记录总数: ', COUNT(*)) as learning_record_count FROM learning_records WHERE deleted = 0;
SELECT CONCAT('用户行为总数: ', COUNT(*)) as behavior_count FROM user_behaviors WHERE deleted = 0;
SELECT CONCAT('推荐记录总数: ', COUNT(*)) as recommendation_count FROM recommendations WHERE deleted = 0;

-- 显示测试账号信息
SELECT '=== 测试账号信息 ===' as info;
SELECT username, email, role, '密码: admin' as password_info FROM users WHERE username = 'admin';
SELECT username, email, role, '密码: user' as password_info FROM users WHERE username = 'user';
SELECT username, email, role, '密码: 123123' as password_info FROM users WHERE username = '123';
