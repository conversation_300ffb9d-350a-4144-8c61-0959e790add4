#!/bin/bash

# 学习平台数据库初始化脚本执行器
# 使用方法: ./run_init.sh

# MySQL配置
MYSQL_BIN="/usr/local/mysql-8.4.6-macos15-arm64/bin"
MYSQL_USER="study250801"
MYSQL_PASSWORD="@yw@%K!@3^Dm"
MYSQL_HOST="localhost"
MYSQL_PORT="3306"
DATABASE_NAME="learning_platform"
SQL_FILE="init_learning_platform.sql"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 学习平台数据库初始化 ===${NC}"
echo -e "${YELLOW}MySQL路径: ${MYSQL_BIN}${NC}"
echo -e "${YELLOW}数据库: ${DATABASE_NAME}${NC}"
echo -e "${YELLOW}用户: ${MYSQL_USER}${NC}"
echo ""

# 检查MySQL是否可用
echo -e "${BLUE}检查MySQL连接...${NC}"
if ! ${MYSQL_BIN}/mysql -h${MYSQL_HOST} -P${MYSQL_PORT} -u${MYSQL_USER} -p${MYSQL_PASSWORD} -e "SELECT 1;" > /dev/null 2>&1; then
    echo -e "${RED}错误: 无法连接到MySQL服务器${NC}"
    echo -e "${RED}请检查MySQL服务是否运行，以及用户名密码是否正确${NC}"
    exit 1
fi
echo -e "${GREEN}MySQL连接成功！${NC}"

# 检查SQL文件是否存在
if [ ! -f "$SQL_FILE" ]; then
    echo -e "${RED}错误: SQL文件 $SQL_FILE 不存在${NC}"
    exit 1
fi

# 执行SQL文件
echo -e "${BLUE}开始执行数据库初始化...${NC}"
echo -e "${YELLOW}这将删除现有的 ${DATABASE_NAME} 数据库并重新创建${NC}"
read -p "确认继续? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}操作已取消${NC}"
    exit 0
fi

echo -e "${BLUE}执行SQL脚本...${NC}"
${MYSQL_BIN}/mysql -h${MYSQL_HOST} -P${MYSQL_PORT} -u${MYSQL_USER} -p${MYSQL_PASSWORD} < ${SQL_FILE}

if [ $? -eq 0 ]; then
    echo -e "${GREEN}=== 数据库初始化完成！ ===${NC}"
    echo ""
    echo -e "${GREEN}测试账号信息:${NC}"
    echo -e "${YELLOW}管理员账号: admin / admin${NC}"
    echo -e "${YELLOW}普通用户账号: user / user${NC}"
    echo -e "${YELLOW}测试账号: 123 / 123123${NC}"
    echo ""
    echo -e "${GREEN}数据统计:${NC}"
    echo -e "${YELLOW}- 20门课程${NC}"
    echo -e "${YELLOW}- 10个用户${NC}"
    echo -e "${YELLOW}- 丰富的学习记录和用户行为数据${NC}"
    echo -e "${YELLOW}- 预生成的推荐数据${NC}"
    echo ""
    echo -e "${GREEN}现在可以启动应用程序进行测试！${NC}"
else
    echo -e "${RED}数据库初始化失败！${NC}"
    exit 1
fi
