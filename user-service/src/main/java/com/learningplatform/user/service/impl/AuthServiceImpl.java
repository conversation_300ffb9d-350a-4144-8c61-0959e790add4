package com.learningplatform.user.service.impl;

import com.learningplatform.user.dto.*;
import com.learningplatform.user.entity.User;
import com.learningplatform.user.service.AuthService;
import com.learningplatform.user.service.UserService;
import com.learningplatform.user.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 认证服务实现类
 */
@Service
public class AuthServiceImpl implements AuthService {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    @Override
    public UserInfoResponse register(UserRegisterRequest request) {
        // 验证密码确认
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new RuntimeException("密码和确认密码不匹配");
        }
        
        // 检查用户名是否已存在
        if (userService.existsByUsername(request.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (userService.existsByEmail(request.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 创建用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setNickname(StringUtils.hasText(request.getNickname()) ? 
                        request.getNickname() : request.getUsername());
        user.setRole(User.UserRole.STUDENT);
        user.setStatus(User.UserStatus.ACTIVE);
        
        User savedUser = userService.createUser(user);
        return new UserInfoResponse(savedUser);
    }
    
    @Override
    public UserLoginResponse login(UserLoginRequest request) {
        // 根据用户名或邮箱查找用户
        User user = null;
        String usernameOrEmail = request.getUsernameOrEmail();
        
        if (usernameOrEmail.contains("@")) {
            // 邮箱登录
            user = userService.findByEmail(usernameOrEmail);
        } else {
            // 用户名登录
            user = userService.findByUsername(usernameOrEmail);
        }
        
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            throw new RuntimeException("密码错误");
        }
        
        // 检查用户状态
        if (user.getStatus() != User.UserStatus.ACTIVE) {
            throw new RuntimeException("用户账户已被禁用");
        }
        
        // 生成JWT令牌
        String token = jwtUtil.generateToken(user.getId(), user.getUsername(), user.getRole().name());
        
        // 返回登录响应
        UserInfoResponse userInfo = new UserInfoResponse(user);
        return new UserLoginResponse(token, userInfo);
    }
}