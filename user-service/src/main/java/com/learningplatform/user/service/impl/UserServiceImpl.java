package com.learningplatform.user.service.impl;

import com.learningplatform.user.entity.User;
import com.learningplatform.user.mapper.UserMapper;
import com.learningplatform.user.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl implements UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Override
    public User findById(Long id) {
        return userMapper.selectById(id);
    }
    
    @Override
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }
    
    @Override
    public User findByEmail(String email) {
        return userMapper.findByEmail(email);
    }
    
    @Override
    public boolean existsByUsername(String username) {
        return userMapper.existsByUsername(username);
    }
    
    @Override
    public boolean existsByEmail(String email) {
        return userMapper.existsByEmail(email);
    }
    
    @Override
    public User createUser(User user) {
        userMapper.insert(user);
        return user;
    }
    
    @Override
    public User updateUser(User user) {
        userMapper.updateById(user);
        return user;
    }
    
    @Override
    public void deleteUser(Long id) {
        userMapper.deleteById(id);
    }
}