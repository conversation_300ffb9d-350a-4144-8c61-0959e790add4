package com.learningplatform.user.service;

import com.learningplatform.user.dto.UserLoginRequest;
import com.learningplatform.user.dto.UserLoginResponse;
import com.learningplatform.user.dto.UserRegisterRequest;
import com.learningplatform.user.dto.UserInfoResponse;

/**
 * 认证服务接口
 */
public interface AuthService {
    
    /**
     * 用户注册
     * @param request 注册请求
     * @return 用户信息
     */
    UserInfoResponse register(UserRegisterRequest request);
    
    /**
     * 用户登录
     * @param request 登录请求
     * @return 登录响应（包含token和用户信息）
     */
    UserLoginResponse login(UserLoginRequest request);
}