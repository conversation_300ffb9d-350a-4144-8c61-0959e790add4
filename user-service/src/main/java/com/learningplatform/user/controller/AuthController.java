package com.learningplatform.user.controller;

import com.learningplatform.common.response.Result;
import com.learningplatform.user.dto.UserLoginRequest;
import com.learningplatform.user.dto.UserLoginResponse;
import com.learningplatform.user.dto.UserRegisterRequest;
import com.learningplatform.user.dto.UserInfoResponse;
import com.learningplatform.user.service.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/api/user")
public class AuthController {
    
    @Autowired
    private AuthService authService;
    
    /**
     * 用户注册
     * @param request 注册请求
     * @return 注册结果
     */
    @PostMapping("/register")
    public Result<UserInfoResponse> register(@Valid @RequestBody UserRegisterRequest request) {
        try {
            UserInfoResponse userInfo = authService.register(request);
            return Result.success(userInfo);
        } catch (Exception e) {
            return Result.error("1003", e.getMessage());
        }
    }
    
    /**
     * 用户登录
     * @param request 登录请求
     * @return 登录结果
     */
    @PostMapping("/login")
    public Result<UserLoginResponse> login(@Valid @RequestBody UserLoginRequest request) {
        try {
            UserLoginResponse response = authService.login(request);
            return Result.success(response);
        } catch (Exception e) {
            return Result.error("1002", e.getMessage());
        }
    }
}