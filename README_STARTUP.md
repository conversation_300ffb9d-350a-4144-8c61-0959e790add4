# SpringCloud微服务学习平台 - 一键启动说明

## 🎯 快速开始

您的系统已经配置完成，可以直接使用一键启动脚本！

### 环境状态 ✅
- **Java**: 23.0.2 (jenv管理，兼容Spring Boot)
- **Maven**: 3.9.10 ✅
- **Node.js**: 22.16.0 ✅
- **MySQL**: 8.4.6 ✅ (服务已运行)
- **Nacos**: ✅ (已运行在端口8848)
- **项目结构**: ✅ (所有微服务目录完整)
- **前端依赖**: ✅ (已安装)

## 🚀 启动命令

### 1. 首次启动（推荐）
```bash
# 设置数据库（仅首次需要）
./setup-database.sh

# 启动所有服务
./start-all-services.sh
```

### 2. 日常启动
```bash
# 直接启动所有服务
./start-all-services.sh
```

### 3. 管理命令
```bash
./start-all-services.sh status   # 查看服务状态
./start-all-services.sh stop     # 停止所有服务
./start-all-services.sh restart  # 重启所有服务
```

## 📊 服务访问地址

启动成功后，您可以访问：

| 服务 | 地址 | 说明 |
|------|------|------|
| **前端应用** | http://localhost:8086 | 主要访问入口 |
| **API网关** | http://localhost:8090 | API统一入口 |
| **Nacos控制台** | http://localhost:8848/nacos | 服务管理 (nacos/nacos) |

## 🧪 测试登录

- **用户名**: `admin`
- **密码**: `admin123`

## 📝 脚本说明

### 主要脚本
- `start-all-services.sh` - 一键启动所有服务
- `setup-database.sh` - 数据库初始化
- `test-environment.sh` - 环境检测

### 启动顺序
脚本会按以下顺序启动服务：
1. Nacos注册中心 (8848) ✅ 已运行
2. User Service (8081)
3. Course Service (8082)
4. Learning Service (8083)
5. Recommendation Service (8084)
6. Discussion Service (8085)
7. Payment Service (8087)
8. Gateway Service (8090)
9. Frontend (8086)

## 📋 数据库配置

### MySQL配置信息
- **数据库**: learning_platform
- **用户**: study250801
- **密码**: @yw@%K!@3^Dm
- **路径**: /usr/local/mysql-8.4.6-macos15-arm64/bin

### 数据库管理
```bash
# 连接数据库
/usr/local/mysql-8.4.6-macos15-arm64/bin/mysql -u study250801 -p learning_platform

# 启动MySQL服务
sudo /usr/local/mysql-8.4.6-macos15-arm64/support-files/mysql.server start
```

## 🔧 故障排查

### 常见问题

1. **端口被占用**
   ```bash
   ./start-all-services.sh stop  # 停止所有服务
   ./start-all-services.sh start # 重新启动
   ```

2. **Java版本问题**
   ```bash
   # 当前使用Java 23，如需切换到Java 22
   jenv local zulu64-22.0.2
   ```

3. **MySQL连接问题**
   ```bash
   # 检查MySQL状态
   ps aux | grep mysql
   
   # 重启MySQL
   sudo /usr/local/mysql-8.4.6-macos15-arm64/support-files/mysql.server restart
   ```

4. **查看日志**
   ```bash
   # 查看所有服务日志
   tail -f *.log
   
   # 查看特定服务日志
   tail -f user-service.log
   tail -f gateway-service.log
   ```

## 🎯 启动验证

启动完成后，请验证：

1. **服务状态检查**
   ```bash
   ./start-all-services.sh status
   ```

2. **前端访问**
   - 打开浏览器访问: http://localhost:8086
   - 使用 admin/admin123 登录

3. **API测试**
   ```bash
   # 测试网关
   curl http://localhost:8090/actuator/health
   
   # 测试登录API
   curl -X POST http://localhost:8090/api/user/login \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"admin123"}'
   ```

## ⚡ 性能优化建议

1. **首次启动**：编译和依赖下载需要时间，请耐心等待
2. **内存使用**：建议系统内存8GB以上
3. **并发启动**：脚本已优化启动顺序，避免服务依赖问题

## 📞 技术支持

如果遇到问题：

1. 运行环境检测：`./test-environment.sh`
2. 查看服务状态：`./start-all-services.sh status`
3. 检查日志文件：`tail -f *.log`
4. 重启服务：`./start-all-services.sh restart`

## 🔄 更新和维护

```bash
# 停止服务
./start-all-services.sh stop

# 清理编译文件
mvn clean

# 重新编译启动
./start-all-services.sh
```

---

**🎉 恭喜！您的SpringCloud微服务学习平台已准备就绪！**

现在可以运行 `./start-all-services.sh` 来启动整个系统。
