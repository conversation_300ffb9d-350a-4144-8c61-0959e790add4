server:
  port: 8082

spring:
  application:
    name: course-service
  
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb
    username: sa
    password: ''
  
  sql:
    init:
      mode: always
  
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: public
        group: DEFAULT_GROUP

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: auto

# 日志配置
logging:
  level:
    com.learningplatform.course: debug
    org.springframework.cloud: debug