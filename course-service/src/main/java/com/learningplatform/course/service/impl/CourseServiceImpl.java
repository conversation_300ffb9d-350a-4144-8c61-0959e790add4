package com.learningplatform.course.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.learningplatform.course.client.LearningServiceClient;
import com.learningplatform.course.dto.CourseCreateRequest;
import com.learningplatform.course.dto.CourseUpdateRequest;
import com.learningplatform.course.entity.Course;
import com.learningplatform.course.entity.CourseChapter;
import com.learningplatform.course.entity.CourseCategory;
import com.learningplatform.course.mapper.CourseChapterMapper;
import com.learningplatform.course.mapper.CourseMapper;
import com.learningplatform.course.mapper.CourseCategoryMapper;
import com.learningplatform.course.service.CourseService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

@Service
public class CourseServiceImpl implements CourseService {
    
    @Autowired
    private CourseMapper courseMapper;

    @Autowired
    private CourseChapterMapper chapterMapper;

    @Autowired
    private CourseCategoryMapper categoryMapper;

    @Autowired
    private LearningServiceClient learningServiceClient;
    
    @Override
    public IPage<Course> getPublishedCourses(Page<Course> page) {
        return courseMapper.selectPublishedCoursesWithDetails(page);
    }
    
    @Override
    public IPage<Course> getCoursesByTeacher(Page<Course> page, Long teacherId) {
        return courseMapper.selectCoursesByTeacher(page, teacherId);
    }
    
    @Override
    public Course getCourseById(Long courseId) {
        Course course = courseMapper.selectCourseWithDetails(courseId);
        if (course != null) {
            // 获取课程章节
            List<CourseChapter> chapters = chapterMapper.selectByCourseId(courseId);
            // 这里可以将chapters设置到course中，但需要在Course实体中添加chapters字段
        }
        return course;
    }
    
    @Override
    public IPage<Course> searchCourses(Page<Course> page, String keyword) {
        return courseMapper.searchCourses(page, keyword);
    }
    
    @Override
    @Transactional
    public Course createCourse(CourseCreateRequest request, Long teacherId) {
        Course course = new Course();
        BeanUtils.copyProperties(request, course);
        course.setTeacherId(teacherId);
        course.setStatus("DRAFT"); // 默认为草稿状态
        
        if (request.getPrice() != null) {
            course.setPrice(BigDecimal.valueOf(request.getPrice()));
        } else {
            course.setPrice(BigDecimal.ZERO);
        }
        
        courseMapper.insert(course);
        
        // 创建章节
        if (request.getChapters() != null && !request.getChapters().isEmpty()) {
            for (int i = 0; i < request.getChapters().size(); i++) {
                var chapterRequest = request.getChapters().get(i);
                CourseChapter chapter = new CourseChapter();
                BeanUtils.copyProperties(chapterRequest, chapter);
                chapter.setCourseId(course.getId());
                chapter.setOrderIndex(i);
                chapterMapper.insert(chapter);
            }
        }
        
        return getCourseById(course.getId());
    }
    
    @Override
    @Transactional
    public Course updateCourse(Long courseId, CourseUpdateRequest request, Long teacherId) {
        Course existingCourse = courseMapper.selectById(courseId);
        if (existingCourse == null) {
            throw new RuntimeException("课程不存在");
        }
        
        if (!existingCourse.getTeacherId().equals(teacherId)) {
            throw new RuntimeException("无权限修改此课程");
        }
        
        Course course = new Course();
        BeanUtils.copyProperties(request, course);
        course.setId(courseId);
        course.setTeacherId(teacherId);
        
        if (request.getPrice() != null) {
            course.setPrice(BigDecimal.valueOf(request.getPrice()));
        }
        
        courseMapper.updateById(course);
        
        // 更新章节 - 简单实现：删除所有章节后重新创建
        if (request.getChapters() != null) {
            QueryWrapper<CourseChapter> wrapper = new QueryWrapper<>();
            wrapper.eq("course_id", courseId);
            chapterMapper.delete(wrapper);
            
            for (int i = 0; i < request.getChapters().size(); i++) {
                var chapterRequest = request.getChapters().get(i);
                CourseChapter chapter = new CourseChapter();
                BeanUtils.copyProperties(chapterRequest, chapter);
                chapter.setCourseId(courseId);
                chapter.setOrderIndex(i);
                chapterMapper.insert(chapter);
            }
        }
        
        return getCourseById(courseId);
    }
    
    @Override
    @Transactional
    public void deleteCourse(Long courseId, Long teacherId) {
        Course existingCourse = courseMapper.selectById(courseId);
        if (existingCourse == null) {
            throw new RuntimeException("课程不存在");
        }
        
        if (!existingCourse.getTeacherId().equals(teacherId)) {
            throw new RuntimeException("无权限删除此课程");
        }
        
        // 删除章节
        QueryWrapper<CourseChapter> wrapper = new QueryWrapper<>();
        wrapper.eq("course_id", courseId);
        chapterMapper.delete(wrapper);
        
        // 删除课程
        courseMapper.deleteById(courseId);
    }
    
    @Override
    public void enrollCourse(Long courseId, Long userId) {
        // 验证课程是否存在和可报名
        Course course = courseMapper.selectById(courseId);
        if (course == null) {
            throw new RuntimeException("课程不存在");
        }

        if (!"PUBLISHED".equals(course.getStatus())) {
            throw new RuntimeException("课程未发布，无法报名");
        }

        // 调用学习服务的报名接口
        try {
            learningServiceClient.enrollCourse(courseId);
        } catch (Exception e) {
            throw new RuntimeException("报名失败: " + e.getMessage());
        }
    }

    @Override
    public List<CourseCategory> getCategories() {
        return categoryMapper.selectAllCategories();
    }

    @Override
    public List<CourseChapter> getCourseChapters(Long courseId) {
        QueryWrapper<CourseChapter> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("course_id", courseId);
        queryWrapper.orderByAsc("order_index");
        return chapterMapper.selectList(queryWrapper);
    }
}