package com.learningplatform.course.client;

import com.learningplatform.common.response.Result;
import com.learningplatform.course.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 学习服务客户端
 */
@FeignClient(name = "learning-service", configuration = FeignConfig.class)
public interface LearningServiceClient {
    
    /**
     * 用户注册课程
     */
    @PostMapping("/api/learning/enroll")
    Result<Object> enrollCourse(@RequestParam("courseId") Long courseId);
}
