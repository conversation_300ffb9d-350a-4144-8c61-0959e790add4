package com.learningplatform.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.learningplatform.course.entity.CourseChapter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface CourseChapterMapper extends BaseMapper<CourseChapter> {
    
    @Select("SELECT * FROM course_chapters WHERE course_id = #{courseId} ORDER BY order_index ASC")
    List<CourseChapter> selectByCourseId(@Param("courseId") Long courseId);
}