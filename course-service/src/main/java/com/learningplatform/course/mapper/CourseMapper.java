package com.learningplatform.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.learningplatform.course.entity.Course;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface CourseMapper extends BaseMapper<Course> {
    
    @Select("SELECT c.*, " +
            "u.username as teacher_name, " +
            "cat.name as category_name, " +
            "(SELECT COUNT(*) FROM course_enrollments ce WHERE ce.course_id = c.id) as students_count, " +
            "(SELECT COUNT(*) FROM course_chapters ch WHERE ch.course_id = c.id) as chapters_count " +
            "FROM courses c " +
            "LEFT JOIN users u ON c.teacher_id = u.id " +
            "LEFT JOIN course_categories cat ON c.category_id = cat.id " +
            "WHERE c.status = 'PUBLISHED' " +
            "ORDER BY c.created_at DESC")
    IPage<Course> selectPublishedCoursesWithDetails(Page<Course> page);
    
    @Select("SELECT c.*, " +
            "u.username as teacher_name, " +
            "cat.name as category_name, " +
            "(SELECT COUNT(*) FROM course_enrollments ce WHERE ce.course_id = c.id) as students_count, " +
            "(SELECT COUNT(*) FROM course_chapters ch WHERE ch.course_id = c.id) as chapters_count " +
            "FROM courses c " +
            "LEFT JOIN users u ON c.teacher_id = u.id " +
            "LEFT JOIN course_categories cat ON c.category_id = cat.id " +
            "WHERE c.teacher_id = #{teacherId} " +
            "ORDER BY c.created_at DESC")
    IPage<Course> selectCoursesByTeacher(Page<Course> page, @Param("teacherId") Long teacherId);
    
    @Select("SELECT c.*, " +
            "u.username as teacher_name, " +
            "cat.name as category_name, " +
            "(SELECT COUNT(*) FROM course_enrollments ce WHERE ce.course_id = c.id) as students_count, " +
            "(SELECT COUNT(*) FROM course_chapters ch WHERE ch.course_id = c.id) as chapters_count " +
            "FROM courses c " +
            "LEFT JOIN users u ON c.teacher_id = u.id " +
            "LEFT JOIN course_categories cat ON c.category_id = cat.id " +
            "WHERE c.id = #{courseId}")
    Course selectCourseWithDetails(@Param("courseId") Long courseId);
    
    @Select("SELECT c.*, " +
            "u.username as teacher_name, " +
            "cat.name as category_name, " +
            "(SELECT COUNT(*) FROM course_enrollments ce WHERE ce.course_id = c.id) as students_count, " +
            "(SELECT COUNT(*) FROM course_chapters ch WHERE ch.course_id = c.id) as chapters_count " +
            "FROM courses c " +
            "LEFT JOIN users u ON c.teacher_id = u.id " +
            "LEFT JOIN course_categories cat ON c.category_id = cat.id " +
            "WHERE c.status = 'PUBLISHED' " +
            "AND (c.title LIKE CONCAT('%', #{keyword}, '%') " +
            "OR c.description LIKE CONCAT('%', #{keyword}, '%') " +
            "OR u.username LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY c.created_at DESC")
    IPage<Course> searchCourses(Page<Course> page, @Param("keyword") String keyword);
}