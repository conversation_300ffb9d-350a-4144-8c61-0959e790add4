package com.learningplatform.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.learningplatform.course.entity.CourseCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface CourseCategoryMapper extends BaseMapper<CourseCategory> {
    
    @Select("SELECT * FROM course_categories ORDER BY name")
    List<CourseCategory> selectAllCategories();
}
