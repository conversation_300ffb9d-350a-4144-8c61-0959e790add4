package com.learningplatform.course.controller;

import org.springframework.web.bind.annotation.*;
import java.util.*;

/**
 * 支付控制器（模拟支付功能）
 */
@RestController
@RequestMapping("/api/payment")
public class PaymentController {
    
    // 模拟支付订单存储
    private final Map<String, Map<String, Object>> paymentOrders = new HashMap<>();
    
    /**
     * 创建支付订单
     */
    @PostMapping("/create")
    public Map<String, Object> createPaymentOrder(@RequestBody Map<String, Object> orderData) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String orderId = "ORDER_" + System.currentTimeMillis();
            
            Map<String, Object> order = new HashMap<>();
            order.put("orderId", orderId);
            order.put("courseId", orderData.get("courseId"));
            order.put("amount", orderData.get("amount"));
            order.put("status", "PENDING");
            order.put("createdAt", new Date());
            
            paymentOrders.put(orderId, order);
            
            response.put("success", true);
            response.put("data", order);
            response.put("message", "支付订单创建成功");
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "创建支付订单失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 模拟支付处理
     */
    @PostMapping("/simulate")
    public Map<String, Object> simulatePayment(@RequestBody Map<String, Object> paymentData) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String simulateResult = (String) paymentData.get("simulateResult");
            Long courseId = Long.valueOf(paymentData.get("courseId").toString());
            Double amount = Double.valueOf(paymentData.get("amount").toString());
            String paymentMethod = (String) paymentData.get("paymentMethod");
            
            String orderId = "ORDER_" + System.currentTimeMillis();
            
            Map<String, Object> paymentResult = new HashMap<>();
            paymentResult.put("orderId", orderId);
            paymentResult.put("courseId", courseId);
            paymentResult.put("amount", amount);
            paymentResult.put("paymentMethod", paymentMethod);
            paymentResult.put("processedAt", new Date());
            
            if ("success".equals(simulateResult)) {
                paymentResult.put("status", "SUCCESS");
                paymentResult.put("transactionId", "TXN_" + System.currentTimeMillis());
                
                response.put("success", true);
                response.put("data", paymentResult);
                response.put("message", "支付成功！您现在可以开始学习课程了。");
                
            } else {
                paymentResult.put("status", "FAILED");
                paymentResult.put("failureReason", "模拟支付失败");
                
                response.put("success", false);
                response.put("data", paymentResult);
                response.put("message", "支付失败，请检查支付信息后重试。");
            }
            
            // 保存支付记录
            paymentOrders.put(orderId, paymentResult);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "支付处理失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 查询支付状态
     */
    @GetMapping("/status/{orderId}")
    public Map<String, Object> getPaymentStatus(@PathVariable String orderId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> order = paymentOrders.get(orderId);
            
            if (order != null) {
                response.put("success", true);
                response.put("data", order);
                response.put("message", "查询成功");
            } else {
                response.put("success", false);
                response.put("message", "订单不存在");
            }
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 获取支付历史记录
     */
    @GetMapping("/history")
    public Map<String, Object> getPaymentHistory() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<Map<String, Object>> history = new ArrayList<>(paymentOrders.values());
            
            // 按时间倒序排列
            history.sort((a, b) -> {
                Date dateA = (Date) a.get("processedAt");
                Date dateB = (Date) b.get("processedAt");
                if (dateA == null) dateA = (Date) a.get("createdAt");
                if (dateB == null) dateB = (Date) b.get("createdAt");
                return dateB.compareTo(dateA);
            });
            
            response.put("success", true);
            response.put("data", history);
            response.put("message", "获取支付历史成功");
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取支付历史失败: " + e.getMessage());
        }
        
        return response;
    }
}
