package com.learningplatform.course.controller;

import com.learningplatform.common.response.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 */
@RestController
@RequestMapping("/api/health")
public class HealthController {
    
    @GetMapping
    public Result<Map<String, String>> health() {
        Map<String, String> status = new HashMap<>();
        status.put("service", "course-service");
        status.put("status", "UP");
        status.put("timestamp", java.time.LocalDateTime.now().toString());
        return Result.success(status);
    }
}