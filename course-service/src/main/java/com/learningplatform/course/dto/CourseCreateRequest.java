package com.learningplatform.course.dto;

import java.util.List;

/**
 * 课程创建请求DTO
 */
public class CourseCreateRequest {
    
    private String title;
    private String description;
    private String coverImage;
    private Long categoryId;
    private Double price;
    private String difficultyLevel;
    private String status;
    private List<ChapterCreateRequest> chapters;
    
    // 构造函数
    public CourseCreateRequest() {}
    
    // Getter和Setter方法
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCoverImage() {
        return coverImage;
    }
    
    public void setCoverImage(String coverImage) {
        this.coverImage = coverImage;
    }
    
    public Long getCategoryId() {
        return categoryId;
    }
    
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }
    
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
    
    public String getDifficultyLevel() {
        return difficultyLevel;
    }
    
    public void setDifficultyLevel(String difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public List<ChapterCreateRequest> getChapters() {
        return chapters;
    }
    
    public void setChapters(List<ChapterCreateRequest> chapters) {
        this.chapters = chapters;
    }
}