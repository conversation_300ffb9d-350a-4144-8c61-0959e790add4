package com.learningplatform.recommendation.controller;

import com.learningplatform.recommendation.model.UserBehaviorModel;
import com.learningplatform.recommendation.model.RecommendationModel;
import com.learningplatform.recommendation.repository.UserBehaviorRepository;
import com.learningplatform.recommendation.service.RecommendationService;
import com.learningplatform.recommendation.service.RecommendationAlgorithmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 推荐服务数据初始化控制器
 */
@RestController
@RequestMapping("/api/recommendation/init")
public class DataInitController {

    @Autowired
    private UserBehaviorRepository userBehaviorRepository;

    @Autowired
    private RecommendationService recommendationService;

    @Autowired
    private RecommendationAlgorithmService algorithmService;
    
    @PostMapping("/test-data")
    public Map<String, Object> initTestData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 清除现有数据
            userBehaviorRepository.findAll().clear();
            
            // 用户1的行为数据
            userBehaviorRepository.save(new UserBehaviorModel(1L, 1L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(1L, 1L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(1L, 2L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(1L, 2L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(1L, 3L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(1L, 4L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(1L, 5L, "VIEW", BigDecimal.valueOf(1.0)));
            
            // 用户2的行为数据
            userBehaviorRepository.save(new UserBehaviorModel(2L, 1L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(2L, 1L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(2L, 1L, "COMPLETE", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(2L, 3L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(2L, 3L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(2L, 4L, "VIEW", BigDecimal.valueOf(1.0)));
            
            // 用户3的行为数据
            userBehaviorRepository.save(new UserBehaviorModel(3L, 2L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(3L, 2L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(3L, 3L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(3L, 4L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(3L, 4L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(3L, 5L, "VIEW", BigDecimal.valueOf(1.0)));
            
            // 用户4的行为数据
            userBehaviorRepository.save(new UserBehaviorModel(4L, 1L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(4L, 2L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(4L, 2L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(4L, 5L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(4L, 5L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(4L, 5L, "COMPLETE", BigDecimal.valueOf(1.0)));
            
            // 用户5的行为数据
            userBehaviorRepository.save(new UserBehaviorModel(5L, 3L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(5L, 3L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(5L, 4L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(5L, 4L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(5L, 1L, "VIEW", BigDecimal.valueOf(1.0)));
            
            // 用户6的行为数据（当前登录用户）
            userBehaviorRepository.save(new UserBehaviorModel(6L, 1L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(6L, 2L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(6L, 3L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(6L, 3L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(6L, 4L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(6L, 4L, "ENROLL", BigDecimal.valueOf(1.0)));
            
            result.put("success", true);
            result.put("message", "推荐服务测试数据初始化成功");
            result.put("totalBehaviors", userBehaviorRepository.findAll().size());
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "初始化失败: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    @GetMapping("/test-recommendation")
    public Map<String, Object> testRecommendation(@RequestParam(defaultValue = "1") Long userId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取用户行为数据
            List<UserBehaviorModel> userBehaviors = userBehaviorRepository.findByUserId(userId);
            result.put("userBehaviors", userBehaviors.size());
            result.put("userBehaviorDetails", userBehaviors);

            // 获取所有用户行为数据
            List<UserBehaviorModel> allBehaviors = userBehaviorRepository.findAll();
            result.put("allBehaviors", allBehaviors.size());

            // 分析用户分布
            Map<Long, Long> userCounts = allBehaviors.stream()
                .collect(Collectors.groupingBy(UserBehaviorModel::getUserId, Collectors.counting()));
            result.put("userCounts", userCounts);

            // 分析课程分布
            Map<Long, Long> courseCounts = allBehaviors.stream()
                .collect(Collectors.groupingBy(UserBehaviorModel::getCourseId, Collectors.counting()));
            result.put("courseCounts", courseCounts);

            // 测试热门课程推荐
            List<RecommendationModel> popular = algorithmService.popularCourseRecommendation(userId, 5);
            result.put("popular", popular);
            result.put("popularCount", popular.size());

            // 测试生成推荐
            List<RecommendationModel> recommendations = recommendationService.generateRecommendations(userId, 5);
            result.put("recommendations", recommendations);
            result.put("recommendationCount", recommendations.size());

            // 测试协同过滤
            List<RecommendationModel> collaborative = recommendationService.getCollaborativeRecommendations(userId, userBehaviors, 3);
            result.put("collaborative", collaborative);
            result.put("collaborativeCount", collaborative.size());

            // 测试简单推荐逻辑
            List<RecommendationModel> simple = getSimpleRecommendations(userId, 3);
            result.put("simple", simple);
            result.put("simpleCount", simple.size());

            result.put("success", true);
            result.put("message", "推荐测试完成");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "测试失败: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    // 简单推荐逻辑：推荐用户没有学过的热门课程
    private List<RecommendationModel> getSimpleRecommendations(Long userId, int limit) {
        List<UserBehaviorModel> allBehaviors = userBehaviorRepository.findAll();
        List<UserBehaviorModel> userBehaviors = userBehaviorRepository.findByUserId(userId);

        // 获取用户已学习的课程
        Set<Long> userCourses = userBehaviors.stream()
            .map(UserBehaviorModel::getCourseId)
            .collect(Collectors.toSet());

        // 统计课程热门度
        Map<Long, Integer> coursePopularity = new HashMap<>();
        for (UserBehaviorModel behavior : allBehaviors) {
            coursePopularity.merge(behavior.getCourseId(), 1, Integer::sum);
        }

        // 推荐用户没有学过的热门课程
        List<RecommendationModel> recommendations = new ArrayList<>();
        coursePopularity.entrySet().stream()
            .filter(entry -> !userCourses.contains(entry.getKey()))
            .sorted(Map.Entry.<Long, Integer>comparingByValue().reversed())
            .limit(limit)
            .forEach(entry -> {
                RecommendationModel rec = new RecommendationModel(
                    userId,
                    entry.getKey(),
                    BigDecimal.valueOf(entry.getValue() * 0.1),
                    "简单推荐算法"
                );
                recommendations.add(rec);
            });

        return recommendations;
    }
}
