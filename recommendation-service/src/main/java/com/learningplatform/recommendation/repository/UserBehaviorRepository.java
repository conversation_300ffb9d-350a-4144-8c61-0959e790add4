package com.learningplatform.recommendation.repository;

import com.learningplatform.recommendation.model.UserBehaviorModel;
import org.springframework.stereotype.Repository;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 用户行为内存存储Repository
 */
@Repository
public class UserBehaviorRepository {
    
    private final Map<Long, UserBehaviorModel> behaviors = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);
    
    /**
     * 保存用户行为
     */
    public UserBehaviorModel save(UserBehaviorModel behavior) {
        if (behavior.getId() == null) {
            behavior.setId(idGenerator.getAndIncrement());
        }
        if (behavior.getCreatedAt() == null) {
            behavior.setCreatedAt(LocalDateTime.now());
        }
        behaviors.put(behavior.getId(), behavior);
        return behavior;
    }
    
    /**
     * 根据用户ID获取用户行为记录
     */
    public List<UserBehaviorModel> findByUserId(Long userId) {
        return behaviors.values().stream()
                .filter(behavior -> behavior.getUserId().equals(userId))
                .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
                .collect(Collectors.toList());
    }
    
    /**
     * 根据课程ID获取用户行为记录
     */
    public List<UserBehaviorModel> findByCourseId(Long courseId) {
        return behaviors.values().stream()
                .filter(behavior -> behavior.getCourseId().equals(courseId))
                .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
                .collect(Collectors.toList());
    }
    
    /**
     * 根据行为类型获取用户行为记录
     */
    public List<UserBehaviorModel> findByBehaviorType(String behaviorType) {
        return behaviors.values().stream()
                .filter(behavior -> behavior.getBehaviorType().equals(behaviorType))
                .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取用户对特定课程的行为记录
     */
    public List<UserBehaviorModel> findByUserIdAndCourseId(Long userId, Long courseId) {
        return behaviors.values().stream()
                .filter(behavior -> behavior.getUserId().equals(userId) && behavior.getCourseId().equals(courseId))
                .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有行为记录
     */
    public List<UserBehaviorModel> findAll() {
        return new ArrayList<>(behaviors.values());
    }
}