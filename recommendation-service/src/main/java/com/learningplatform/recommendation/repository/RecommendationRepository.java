package com.learningplatform.recommendation.repository;

import com.learningplatform.recommendation.model.RecommendationModel;
import org.springframework.stereotype.Repository;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 推荐结果内存存储Repository
 */
@Repository
public class RecommendationRepository {
    
    private final Map<Long, RecommendationModel> recommendations = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);
    
    /**
     * 保存推荐结果
     */
    public RecommendationModel save(RecommendationModel recommendation) {
        if (recommendation.getId() == null) {
            recommendation.setId(idGenerator.getAndIncrement());
        }
        if (recommendation.getCreatedAt() == null) {
            recommendation.setCreatedAt(LocalDateTime.now());
        }
        recommendations.put(recommendation.getId(), recommendation);
        return recommendation;
    }
    
    /**
     * 根据用户ID获取推荐结果，按分数降序排列
     */
    public List<RecommendationModel> findByUserId(Long userId) {
        return recommendations.values().stream()
                .filter(rec -> rec.getUserId().equals(userId))
                .sorted((a, b) -> {
                    int scoreCompare = b.getScore().compareTo(a.getScore());
                    if (scoreCompare != 0) return scoreCompare;
                    return b.getCreatedAt().compareTo(a.getCreatedAt());
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 根据用户ID获取指定数量的推荐结果
     */
    public List<RecommendationModel> findByUserIdWithLimit(Long userId, Integer limit) {
        return findByUserId(userId).stream()
                .limit(limit)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据课程ID获取推荐该课程的用户列表
     */
    public List<RecommendationModel> findByCourseId(Long courseId) {
        return recommendations.values().stream()
                .filter(rec -> rec.getCourseId().equals(courseId))
                .sorted((a, b) -> b.getScore().compareTo(a.getScore()))
                .collect(Collectors.toList());
    }
    
    /**
     * 删除用户的旧推荐记录
     */
    public int deleteByUserId(Long userId) {
        List<Long> toDelete = recommendations.values().stream()
                .filter(rec -> rec.getUserId().equals(userId))
                .map(RecommendationModel::getId)
                .collect(Collectors.toList());
        
        toDelete.forEach(recommendations::remove);
        return toDelete.size();
    }
    
    /**
     * 检查用户是否已有推荐记录
     */
    public int countByUserId(Long userId) {
        return (int) recommendations.values().stream()
                .filter(rec -> rec.getUserId().equals(userId))
                .count();
    }
}