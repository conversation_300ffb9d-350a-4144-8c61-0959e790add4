package com.learningplatform.recommendation.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户行为模型类（内存存储版本）
 */
public class UserBehaviorModel {
    
    private Long id;
    private Long userId;
    private Long courseId;
    private String behaviorType; // VIEW, ENROLL, COMPLETE, RATE
    private BigDecimal behaviorValue; // 用于评分等数值行为
    private LocalDateTime createdAt;
    
    public UserBehaviorModel() {}
    
    public UserBehaviorModel(Long userId, Long courseId, String behaviorType, BigDecimal behaviorValue) {
        this.userId = userId;
        this.courseId = courseId;
        this.behaviorType = behaviorType;
        this.behaviorValue = behaviorValue;
        this.createdAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Long getCourseId() {
        return courseId;
    }
    
    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }
    
    public String getBehaviorType() {
        return behaviorType;
    }
    
    public void setBehaviorType(String behaviorType) {
        this.behaviorType = behaviorType;
    }
    
    public BigDecimal getBehaviorValue() {
        return behaviorValue;
    }
    
    public void setBehaviorValue(BigDecimal behaviorValue) {
        this.behaviorValue = behaviorValue;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}