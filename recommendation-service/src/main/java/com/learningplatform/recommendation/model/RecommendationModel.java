package com.learningplatform.recommendation.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 推荐结果模型类（内存存储版本）
 */
public class RecommendationModel {
    
    private Long id;
    private Long userId;
    private Long courseId;
    private BigDecimal score; // 推荐分数
    private String reason; // 推荐理由
    private LocalDateTime createdAt;
    
    public RecommendationModel() {}
    
    public RecommendationModel(Long userId, Long courseId, BigDecimal score, String reason) {
        this.userId = userId;
        this.courseId = courseId;
        this.score = score;
        this.reason = reason;
        this.createdAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Long getCourseId() {
        return courseId;
    }
    
    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }
    
    public BigDecimal getScore() {
        return score;
    }
    
    public void setScore(BigDecimal score) {
        this.score = score;
    }
    
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}