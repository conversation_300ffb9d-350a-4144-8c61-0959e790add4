package com.learningplatform.recommendation.service;

import com.learningplatform.recommendation.model.RecommendationModel;
import com.learningplatform.recommendation.model.UserBehaviorModel;
import java.util.List;

/**
 * 推荐服务接口
 */
public interface RecommendationService {
    
    /**
     * 为用户生成课程推荐
     * @param userId 用户ID
     * @param limit 推荐数量限制
     * @return 推荐结果列表
     */
    List<RecommendationModel> generateRecommendations(Long userId, Integer limit);
    
    /**
     * 获取用户的推荐结果
     * @param userId 用户ID
     * @param limit 推荐数量限制
     * @return 推荐结果列表
     */
    List<RecommendationModel> getUserRecommendations(Long userId, Integer limit);
    
    /**
     * 记录用户行为
     * @param userBehavior 用户行为对象
     */
    void recordUserBehavior(UserBehaviorModel userBehavior);
    
    /**
     * 获取相关课程推荐
     * @param courseId 课程ID
     * @param limit 推荐数量限制
     * @return 相关课程推荐列表
     */
    List<RecommendationModel> getRelatedCourses(Long courseId, Integer limit);
    
    /**
     * 处理推荐反馈
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param feedback 反馈类型（LIKE, DISLIKE, CLICK等）
     */
    void processFeedback(Long userId, Long courseId, String feedback);
    
    /**
     * 获取用户行为数据
     * @param userId 用户ID
     * @return 用户行为列表
     */
    List<UserBehaviorModel> getUserBehaviors(Long userId);
    
    /**
     * 获取协同过滤推荐
     * @param userId 用户ID
     * @param userBehaviors 用户行为列表
     * @param limit 推荐数量限制
     * @return 推荐结果列表
     */
    List<RecommendationModel> getCollaborativeRecommendations(Long userId, List<UserBehaviorModel> userBehaviors, Integer limit);
    
    /**
     * 获取内容过滤推荐
     * @param userId 用户ID
     * @param userBehaviors 用户行为列表
     * @param limit 推荐数量限制
     * @return 推荐结果列表
     */
    List<RecommendationModel> getContentBasedRecommendations(Long userId, List<UserBehaviorModel> userBehaviors, Integer limit);
    
    /**
     * 获取混合推荐
     * @param userId 用户ID
     * @param userBehaviors 用户行为列表
     * @param limit 推荐数量限制
     * @return 推荐结果列表
     */
    List<RecommendationModel> getHybridRecommendations(Long userId, List<UserBehaviorModel> userBehaviors, Integer limit);
}