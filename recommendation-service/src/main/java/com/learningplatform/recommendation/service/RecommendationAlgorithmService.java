package com.learningplatform.recommendation.service;

import com.learningplatform.recommendation.model.RecommendationModel;
import com.learningplatform.recommendation.model.UserBehaviorModel;
import java.util.List;

/**
 * 推荐算法服务接口
 */
public interface RecommendationAlgorithmService {
    
    /**
     * 基于用户行为的协同过滤推荐
     * @param userId 用户ID
     * @param userBehaviors 用户行为列表
     * @param limit 推荐数量限制
     * @return 推荐结果列表
     */
    List<RecommendationModel> collaborativeFiltering(Long userId, List<UserBehaviorModel> userBehaviors, Integer limit);
    
    /**
     * 基于课程内容的推荐算法
     * @param userId 用户ID
     * @param userBehaviors 用户行为列表
     * @param limit 推荐数量限制
     * @return 推荐结果列表
     */
    List<RecommendationModel> contentBasedFiltering(Long userId, List<UserBehaviorModel> userBehaviors, Integer limit);
    
    /**
     * 混合推荐算法（结合协同过滤和内容过滤）
     * @param userId 用户ID
     * @param userBehaviors 用户行为列表
     * @param limit 推荐数量限制
     * @return 推荐结果列表
     */
    List<RecommendationModel> hybridRecommendation(Long userId, List<UserBehaviorModel> userBehaviors, Integer limit);
    
    /**
     * 热门课程推荐（用于冷启动）
     * @param userId 用户ID
     * @param limit 推荐数量限制
     * @return 推荐结果列表
     */
    List<RecommendationModel> popularCourseRecommendation(Long userId, Integer limit);
}