package com.learningplatform.recommendation.service.impl;

import com.learningplatform.recommendation.model.RecommendationModel;
import com.learningplatform.recommendation.model.UserBehaviorModel;
import com.learningplatform.recommendation.repository.RecommendationRepository;
import com.learningplatform.recommendation.repository.UserBehaviorRepository;
import com.learningplatform.recommendation.service.RecommendationService;
import com.learningplatform.recommendation.service.RecommendationAlgorithmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * 推荐服务实现类
 */
@Service
public class RecommendationServiceImpl implements RecommendationService {
    
    @Autowired
    private RecommendationRepository recommendationRepository;
    
    @Autowired
    private UserBehaviorRepository userBehaviorRepository;
    
    @Autowired
    private RecommendationAlgorithmService algorithmService;
    
    @Override
    public List<RecommendationModel> generateRecommendations(Long userId, Integer limit) {
        // 获取用户历史行为
        List<UserBehaviorModel> userBehaviors = userBehaviorRepository.findByUserId(userId);
        
        List<RecommendationModel> recommendations;
        
        if (userBehaviors.isEmpty()) {
            // 如果用户没有历史行为，返回热门课程推荐
            recommendations = algorithmService.popularCourseRecommendation(userId, limit);
        } else if (userBehaviors.size() < 3) {
            // 行为数据较少，使用内容过滤
            recommendations = algorithmService.contentBasedFiltering(userId, userBehaviors, limit);
        } else {
            // 行为数据充足，使用混合推荐算法
            recommendations = algorithmService.hybridRecommendation(userId, userBehaviors, limit);
        }
        
        // 清除用户旧的推荐记录
        recommendationRepository.deleteByUserId(userId);
        
        // 保存新的推荐结果
        for (RecommendationModel recommendation : recommendations) {
            recommendationRepository.save(recommendation);
        }
        
        return recommendations;
    }
    
    @Override
    public List<RecommendationModel> getUserRecommendations(Long userId, Integer limit) {
        List<RecommendationModel> recommendations = recommendationRepository.findByUserIdWithLimit(userId, limit);
        
        // 如果没有推荐记录，生成新的推荐
        if (recommendations.isEmpty()) {
            return generateRecommendations(userId, limit);
        }
        
        return recommendations;
    }
    
    @Override
    public void recordUserBehavior(UserBehaviorModel userBehavior) {
        userBehaviorRepository.save(userBehavior);
    }
    
    @Override
    public List<RecommendationModel> getRelatedCourses(Long courseId, Integer limit) {
        // 获取学习过该课程的用户
        List<UserBehaviorModel> courseBehaviors = userBehaviorRepository.findByCourseId(courseId);
        
        if (courseBehaviors.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 统计这些用户还学习了哪些其他课程
        Map<Long, Integer> relatedCourseCount = new HashMap<>();
        
        for (UserBehaviorModel behavior : courseBehaviors) {
            List<UserBehaviorModel> userOtherBehaviors = userBehaviorRepository.findByUserId(behavior.getUserId());
            
            for (UserBehaviorModel otherBehavior : userOtherBehaviors) {
                if (!otherBehavior.getCourseId().equals(courseId)) {
                    relatedCourseCount.merge(otherBehavior.getCourseId(), 1, Integer::sum);
                }
            }
        }
        
        // 按相关度排序并生成推荐
        List<RecommendationModel> relatedRecommendations = new ArrayList<>();
        relatedCourseCount.entrySet().stream()
            .sorted(Map.Entry.<Long, Integer>comparingByValue().reversed())
            .limit(limit)
            .forEach(entry -> {
                BigDecimal score = BigDecimal.valueOf(entry.getValue()).divide(BigDecimal.valueOf(courseBehaviors.size()), 2, BigDecimal.ROUND_HALF_UP);
                RecommendationModel recommendation = new RecommendationModel(null, entry.getKey(), score, "基于相似用户学习行为推荐");
                relatedRecommendations.add(recommendation);
            });
        
        return relatedRecommendations;
    }
    
    @Override
    public void processFeedback(Long userId, Long courseId, String feedback) {
        BigDecimal feedbackValue = BigDecimal.ZERO;
        
        switch (feedback.toUpperCase()) {
            case "LIKE":
                feedbackValue = BigDecimal.valueOf(1.0);
                break;
            case "DISLIKE":
                feedbackValue = BigDecimal.valueOf(-1.0);
                break;
            case "CLICK":
                feedbackValue = BigDecimal.valueOf(0.5);
                break;
            default:
                feedbackValue = BigDecimal.valueOf(0.1);
        }
        
        UserBehaviorModel feedbackBehavior = new UserBehaviorModel(userId, courseId, "FEEDBACK", feedbackValue);
        recordUserBehavior(feedbackBehavior);
    }
    
    @Override
    public List<UserBehaviorModel> getUserBehaviors(Long userId) {
        return userBehaviorRepository.findByUserId(userId);
    }
    
    @Override
    public List<RecommendationModel> getCollaborativeRecommendations(Long userId, List<UserBehaviorModel> userBehaviors, Integer limit) {
        return algorithmService.collaborativeFiltering(userId, userBehaviors, limit);
    }
    
    @Override
    public List<RecommendationModel> getContentBasedRecommendations(Long userId, List<UserBehaviorModel> userBehaviors, Integer limit) {
        return algorithmService.contentBasedFiltering(userId, userBehaviors, limit);
    }
    
    @Override
    public List<RecommendationModel> getHybridRecommendations(Long userId, List<UserBehaviorModel> userBehaviors, Integer limit) {
        return algorithmService.hybridRecommendation(userId, userBehaviors, limit);
    }
    

}