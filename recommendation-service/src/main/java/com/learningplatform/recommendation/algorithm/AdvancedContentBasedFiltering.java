package com.learningplatform.recommendation.algorithm;

import com.learningplatform.recommendation.model.UserBehaviorModel;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 高级内容过滤算法实现
 * 基于TF-IDF和用户偏好建模
 */
public class AdvancedContentBasedFiltering {
    
    private final Map<String, Double> behaviorWeights;
    
    public AdvancedContentBasedFiltering() {
        this.behaviorWeights = Map.of(
            "VIEW", 0.1,
            "ENROLL", 0.5,
            "COMPLETE", 1.0,
            "RATE", 0.8,
            "FEEDBACK", 0.3
        );
    }
    
    /**
     * 基于内容的推荐算法
     */
    public List<CourseRecommendation> contentBasedRecommendation(
            Long targetUserId, 
            List<UserBehaviorModel> allBehaviors, 
            int topN) {
        
        // 构建用户偏好模型
        UserProfile userProfile = buildUserProfile(targetUserId, allBehaviors);
        
        if (userProfile.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 获取用户已学习的课程
        Set<Long> userCourses = allBehaviors.stream()
                .filter(b -> b.getUserId().equals(targetUserId))
                .map(UserBehaviorModel::getCourseId)
                .collect(Collectors.toSet());
        
        // 构建课程特征模型
        Map<Long, CourseProfile> courseProfiles = buildCourseProfiles(allBehaviors);
        
        // 计算课程与用户偏好的相似度
        Map<Long, Double> courseScores = new HashMap<>();
        
        for (Map.Entry<Long, CourseProfile> entry : courseProfiles.entrySet()) {
            Long courseId = entry.getKey();
            CourseProfile courseProfile = entry.getValue();
            
            // 跳过用户已学习的课程
            if (userCourses.contains(courseId)) {
                continue;
            }
            
            // 计算相似度
            double similarity = calculateProfileSimilarity(userProfile, courseProfile);
            if (similarity > 0) {
                courseScores.put(courseId, similarity);
            }
        }
        
        // 排序并返回推荐结果
        return courseScores.entrySet().stream()
                .map(entry -> new CourseRecommendation(
                    entry.getKey(), 
                    entry.getValue(),
                    "基于内容过滤推荐"
                ))
                .sorted((a, b) -> Double.compare(b.score, a.score))
                .limit(topN)
                .collect(Collectors.toList());
    }
    
    /**
     * 基于TF-IDF的推荐算法
     */
    public List<CourseRecommendation> tfidfBasedRecommendation(
            Long targetUserId, 
            List<UserBehaviorModel> allBehaviors, 
            int topN) {
        
        // 构建文档-词项矩阵（课程-行为类型矩阵）
        Map<Long, Map<String, Double>> courseBehaviorMatrix = buildCourseBehaviorMatrix(allBehaviors);
        
        // 计算TF-IDF权重
        Map<Long, Map<String, Double>> tfidfMatrix = calculateTFIDF(courseBehaviorMatrix);
        
        // 构建用户查询向量
        Map<String, Double> userQuery = buildUserQuery(targetUserId, allBehaviors);
        
        if (userQuery.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 获取用户已学习的课程
        Set<Long> userCourses = allBehaviors.stream()
                .filter(b -> b.getUserId().equals(targetUserId))
                .map(UserBehaviorModel::getCourseId)
                .collect(Collectors.toSet());
        
        // 计算余弦相似度
        Map<Long, Double> courseScores = new HashMap<>();
        
        for (Map.Entry<Long, Map<String, Double>> entry : tfidfMatrix.entrySet()) {
            Long courseId = entry.getKey();
            Map<String, Double> courseTfidf = entry.getValue();
            
            if (userCourses.contains(courseId)) {
                continue;
            }
            
            double similarity = calculateCosineSimilarity(userQuery, courseTfidf);
            if (similarity > 0) {
                courseScores.put(courseId, similarity);
            }
        }
        
        return courseScores.entrySet().stream()
                .map(entry -> new CourseRecommendation(
                    entry.getKey(), 
                    entry.getValue(),
                    "基于TF-IDF推荐"
                ))
                .sorted((a, b) -> Double.compare(b.score, a.score))
                .limit(topN)
                .collect(Collectors.toList());
    }
    
    private UserProfile buildUserProfile(Long userId, List<UserBehaviorModel> allBehaviors) {
        Map<String, Double> behaviorPreferences = new HashMap<>();
        Map<Long, Double> courseRatings = new HashMap<>();
        
        List<UserBehaviorModel> userBehaviors = allBehaviors.stream()
                .filter(b -> b.getUserId().equals(userId))
                .collect(Collectors.toList());
        
        if (userBehaviors.isEmpty()) {
            return new UserProfile(behaviorPreferences, courseRatings);
        }
        
        // 计算行为类型偏好
        Map<String, Integer> behaviorCounts = new HashMap<>();
        double totalWeight = 0.0;
        
        for (UserBehaviorModel behavior : userBehaviors) {
            String behaviorType = behavior.getBehaviorType();
            Double weight = behaviorWeights.getOrDefault(behaviorType, 0.1);
            Double behaviorValue = behavior.getBehaviorValue() != null ? 
                behavior.getBehaviorValue().doubleValue() : 1.0;
            
            behaviorCounts.merge(behaviorType, 1, Integer::sum);
            behaviorPreferences.merge(behaviorType, weight * behaviorValue, Double::sum);
            totalWeight += weight * behaviorValue;
            
            // 记录课程评分
            courseRatings.merge(behavior.getCourseId(), weight * behaviorValue, Double::sum);
        }
        
        // 归一化偏好权重
        final double finalTotalWeight = totalWeight;
        if (finalTotalWeight > 0) {
            behaviorPreferences.replaceAll((k, v) -> v / finalTotalWeight);
        }
        
        return new UserProfile(behaviorPreferences, courseRatings);
    }
    
    private Map<Long, CourseProfile> buildCourseProfiles(List<UserBehaviorModel> allBehaviors) {
        Map<Long, CourseProfile> courseProfiles = new HashMap<>();
        
        // 按课程分组行为数据
        Map<Long, List<UserBehaviorModel>> courseGroups = allBehaviors.stream()
                .collect(Collectors.groupingBy(UserBehaviorModel::getCourseId));
        
        for (Map.Entry<Long, List<UserBehaviorModel>> entry : courseGroups.entrySet()) {
            Long courseId = entry.getKey();
            List<UserBehaviorModel> courseBehaviors = entry.getValue();
            
            Map<String, Double> behaviorDistribution = new HashMap<>();
            Map<Long, Double> userRatings = new HashMap<>();
            
            double totalBehaviors = courseBehaviors.size();
            
            for (UserBehaviorModel behavior : courseBehaviors) {
                String behaviorType = behavior.getBehaviorType();
                Double weight = behaviorWeights.getOrDefault(behaviorType, 0.1);
                Double behaviorValue = behavior.getBehaviorValue() != null ? 
                    behavior.getBehaviorValue().doubleValue() : 1.0;
                
                behaviorDistribution.merge(behaviorType, 1.0 / totalBehaviors, Double::sum);
                userRatings.merge(behavior.getUserId(), weight * behaviorValue, Double::sum);
            }
            
            courseProfiles.put(courseId, new CourseProfile(behaviorDistribution, userRatings));
        }
        
        return courseProfiles;
    }
    
    private double calculateProfileSimilarity(UserProfile userProfile, CourseProfile courseProfile) {
        // 计算行为偏好相似度
        double behaviorSimilarity = calculateCosineSimilarity(
            userProfile.behaviorPreferences, 
            courseProfile.behaviorDistribution
        );
        
        // 计算基于其他用户评分的相似度
        double ratingSimilarity = calculateRatingSimilarity(userProfile, courseProfile);
        
        // 加权组合
        return 0.7 * behaviorSimilarity + 0.3 * ratingSimilarity;
    }
    
    private double calculateRatingSimilarity(UserProfile userProfile, CourseProfile courseProfile) {
        // 找到用户学习过的课程中，与目标课程有共同学习者的课程
        Set<Long> commonUsers = new HashSet<>();
        
        for (Long courseId : userProfile.courseRatings.keySet()) {
            // 这里简化处理，实际应该查找共同学习者
            commonUsers.addAll(courseProfile.userRatings.keySet());
        }
        
        if (commonUsers.isEmpty()) {
            return 0.0;
        }
        
        // 计算平均评分相似度
        double avgUserRating = userProfile.courseRatings.values().stream()
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(0.0);
        
        double avgCourseRating = courseProfile.userRatings.values().stream()
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(0.0);
        
        // 使用高斯相似度函数
        double diff = Math.abs(avgUserRating - avgCourseRating);
        return Math.exp(-diff * diff / 2.0);
    }
    
    private Map<Long, Map<String, Double>> buildCourseBehaviorMatrix(List<UserBehaviorModel> allBehaviors) {
        Map<Long, Map<String, Double>> matrix = new HashMap<>();
        
        for (UserBehaviorModel behavior : allBehaviors) {
            Long courseId = behavior.getCourseId();
            String behaviorType = behavior.getBehaviorType();
            Double weight = behaviorWeights.getOrDefault(behaviorType, 0.1);
            
            matrix.computeIfAbsent(courseId, k -> new HashMap<>())
                  .merge(behaviorType, weight, Double::sum);
        }
        
        return matrix;
    }
    
    private Map<Long, Map<String, Double>> calculateTFIDF(Map<Long, Map<String, Double>> courseBehaviorMatrix) {
        // 计算文档频率(DF)
        Map<String, Integer> documentFrequency = new HashMap<>();
        int totalDocuments = courseBehaviorMatrix.size();
        
        for (Map<String, Double> behaviorCounts : courseBehaviorMatrix.values()) {
            for (String behaviorType : behaviorCounts.keySet()) {
                documentFrequency.merge(behaviorType, 1, Integer::sum);
            }
        }
        
        // 计算TF-IDF
        Map<Long, Map<String, Double>> tfidfMatrix = new HashMap<>();
        
        for (Map.Entry<Long, Map<String, Double>> entry : courseBehaviorMatrix.entrySet()) {
            Long courseId = entry.getKey();
            Map<String, Double> behaviorCounts = entry.getValue();
            
            Map<String, Double> tfidf = new HashMap<>();
            double totalTerms = behaviorCounts.values().stream().mapToDouble(Double::doubleValue).sum();
            
            for (Map.Entry<String, Double> termEntry : behaviorCounts.entrySet()) {
                String behaviorType = termEntry.getKey();
                Double termFreq = termEntry.getValue();
                
                // TF = term frequency / total terms in document
                double tf = termFreq / totalTerms;
                
                // IDF = log(total documents / documents containing term)
                double idf = Math.log((double) totalDocuments / documentFrequency.get(behaviorType));
                
                tfidf.put(behaviorType, tf * idf);
            }
            
            tfidfMatrix.put(courseId, tfidf);
        }
        
        return tfidfMatrix;
    }
    
    private Map<String, Double> buildUserQuery(Long userId, List<UserBehaviorModel> allBehaviors) {
        Map<String, Double> query = new HashMap<>();
        
        List<UserBehaviorModel> userBehaviors = allBehaviors.stream()
                .filter(b -> b.getUserId().equals(userId))
                .collect(Collectors.toList());
        
        double totalWeight = 0.0;
        
        for (UserBehaviorModel behavior : userBehaviors) {
            String behaviorType = behavior.getBehaviorType();
            Double weight = behaviorWeights.getOrDefault(behaviorType, 0.1);
            
            query.merge(behaviorType, weight, Double::sum);
            totalWeight += weight;
        }
        
        // 归一化查询向量
        final double finalTotalWeight = totalWeight;
        if (finalTotalWeight > 0) {
            query.replaceAll((k, v) -> v / finalTotalWeight);
        }
        
        return query;
    }
    
    private double calculateCosineSimilarity(Map<String, Double> vectorA, Map<String, Double> vectorB) {
        Set<String> commonKeys = new HashSet<>(vectorA.keySet());
        commonKeys.retainAll(vectorB.keySet());
        
        if (commonKeys.isEmpty()) {
            return 0.0;
        }
        
        double dotProduct = 0.0;
        double normA = 0.0;
        double normB = 0.0;
        
        for (String key : commonKeys) {
            double valueA = vectorA.get(key);
            double valueB = vectorB.get(key);
            
            dotProduct += valueA * valueB;
            normA += valueA * valueA;
            normB += valueB * valueB;
        }
        
        if (normA == 0.0 || normB == 0.0) {
            return 0.0;
        }
        
        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }
    
    private static class UserProfile {
        final Map<String, Double> behaviorPreferences;
        final Map<Long, Double> courseRatings;
        
        UserProfile(Map<String, Double> behaviorPreferences, Map<Long, Double> courseRatings) {
            this.behaviorPreferences = behaviorPreferences;
            this.courseRatings = courseRatings;
        }
        
        boolean isEmpty() {
            return behaviorPreferences.isEmpty() && courseRatings.isEmpty();
        }
    }
    
    private static class CourseProfile {
        final Map<String, Double> behaviorDistribution;
        final Map<Long, Double> userRatings;
        
        CourseProfile(Map<String, Double> behaviorDistribution, Map<Long, Double> userRatings) {
            this.behaviorDistribution = behaviorDistribution;
            this.userRatings = userRatings;
        }
    }
    
    public static class CourseRecommendation {
        public final Long courseId;
        public final double score;
        public final String reason;
        
        public CourseRecommendation(Long courseId, double score, String reason) {
            this.courseId = courseId;
            this.score = score;
            this.reason = reason;
        }
    }
}