package com.learningplatform.recommendation.algorithm;

import java.util.*;

/**
 * 矩阵分解算法实现
 * 使用随机梯度下降(SGD)进行矩阵分解
 */
public class MatrixFactorization {
    
    private final int factors; // 潜在因子数量
    private final double learningRate; // 学习率
    private final double regularization; // 正则化参数
    private final int iterations; // 迭代次数
    
    private double[][] userFeatures; // 用户特征矩阵
    private double[][] itemFeatures; // 物品特征矩阵
    private Map<Long, Integer> userIndexMap; // 用户ID到索引的映射
    private Map<Long, Integer> itemIndexMap; // 物品ID到索引的映射
    
    public MatrixFactorization(int factors, double learningRate, double regularization, int iterations) {
        this.factors = factors;
        this.learningRate = learningRate;
        this.regularization = regularization;
        this.iterations = iterations;
    }
    
    /**
     * 训练矩阵分解模型
     * @param userItemMatrix 用户-物品评分矩阵
     */
    public void train(Map<Long, Map<Long, Double>> userItemMatrix) {
        // 构建索引映射
        buildIndexMaps(userItemMatrix);
        
        int numUsers = userIndexMap.size();
        int numItems = itemIndexMap.size();
        
        // 初始化特征矩阵
        initializeFeatures(numUsers, numItems);
        
        // 准备训练数据
        List<Rating> ratings = prepareTrainingData(userItemMatrix);
        
        // 随机梯度下降训练
        for (int iter = 0; iter < iterations; iter++) {
            Collections.shuffle(ratings); // 随机打乱训练数据
            
            for (Rating rating : ratings) {
                int userIndex = userIndexMap.get(rating.userId);
                int itemIndex = itemIndexMap.get(rating.itemId);
                
                // 计算预测评分
                double prediction = predict(userIndex, itemIndex);
                double error = rating.rating - prediction;
                
                // 更新特征向量
                updateFeatures(userIndex, itemIndex, error);
            }
        }
    }
    
    /**
     * 预测用户对物品的评分
     */
    public double predict(Long userId, Long itemId) {
        Integer userIndex = userIndexMap.get(userId);
        Integer itemIndex = itemIndexMap.get(itemId);
        
        if (userIndex == null || itemIndex == null) {
            return 0.0; // 未知用户或物品返回0
        }
        
        return predict(userIndex, itemIndex);
    }
    
    /**
     * 为用户推荐物品
     * @param userId 用户ID
     * @param excludeItems 需要排除的物品ID集合
     * @param topN 推荐数量
     * @return 推荐的物品ID列表，按预测评分降序排列
     */
    public List<Long> recommend(Long userId, Set<Long> excludeItems, int topN) {
        Integer userIndex = userIndexMap.get(userId);
        if (userIndex == null) {
            return new ArrayList<>();
        }
        
        List<ItemScore> itemScores = new ArrayList<>();
        
        for (Map.Entry<Long, Integer> entry : itemIndexMap.entrySet()) {
            Long itemId = entry.getKey();
            Integer itemIndex = entry.getValue();
            
            if (!excludeItems.contains(itemId)) {
                double score = predict(userIndex, itemIndex);
                itemScores.add(new ItemScore(itemId, score));
            }
        }
        
        // 按分数降序排序
        itemScores.sort((a, b) -> Double.compare(b.score, a.score));
        
        return itemScores.stream()
                .limit(topN)
                .map(item -> item.itemId)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * 计算用户相似度
     */
    public double calculateUserSimilarity(Long userId1, Long userId2) {
        Integer userIndex1 = userIndexMap.get(userId1);
        Integer userIndex2 = userIndexMap.get(userId2);
        
        if (userIndex1 == null || userIndex2 == null) {
            return 0.0;
        }
        
        return cosineSimilarity(userFeatures[userIndex1], userFeatures[userIndex2]);
    }
    
    /**
     * 计算物品相似度
     */
    public double calculateItemSimilarity(Long itemId1, Long itemId2) {
        Integer itemIndex1 = itemIndexMap.get(itemId1);
        Integer itemIndex2 = itemIndexMap.get(itemId2);
        
        if (itemIndex1 == null || itemIndex2 == null) {
            return 0.0;
        }
        
        return cosineSimilarity(itemFeatures[itemIndex1], itemFeatures[itemIndex2]);
    }
    
    private void buildIndexMaps(Map<Long, Map<Long, Double>> userItemMatrix) {
        userIndexMap = new HashMap<>();
        itemIndexMap = new HashMap<>();
        
        int userIndex = 0;
        int itemIndex = 0;
        
        Set<Long> allItems = new HashSet<>();
        
        for (Map.Entry<Long, Map<Long, Double>> entry : userItemMatrix.entrySet()) {
            Long userId = entry.getKey();
            userIndexMap.put(userId, userIndex++);
            allItems.addAll(entry.getValue().keySet());
        }
        
        for (Long itemId : allItems) {
            itemIndexMap.put(itemId, itemIndex++);
        }
    }
    
    private void initializeFeatures(int numUsers, int numItems) {
        Random random = new Random();
        
        userFeatures = new double[numUsers][factors];
        itemFeatures = new double[numItems][factors];
        
        // 随机初始化特征矩阵
        for (int i = 0; i < numUsers; i++) {
            for (int f = 0; f < factors; f++) {
                userFeatures[i][f] = random.nextGaussian() * 0.1;
            }
        }
        
        for (int i = 0; i < numItems; i++) {
            for (int f = 0; f < factors; f++) {
                itemFeatures[i][f] = random.nextGaussian() * 0.1;
            }
        }
    }
    
    private List<Rating> prepareTrainingData(Map<Long, Map<Long, Double>> userItemMatrix) {
        List<Rating> ratings = new ArrayList<>();
        
        for (Map.Entry<Long, Map<Long, Double>> userEntry : userItemMatrix.entrySet()) {
            Long userId = userEntry.getKey();
            for (Map.Entry<Long, Double> itemEntry : userEntry.getValue().entrySet()) {
                Long itemId = itemEntry.getKey();
                Double rating = itemEntry.getValue();
                ratings.add(new Rating(userId, itemId, rating));
            }
        }
        
        return ratings;
    }
    
    private double predict(int userIndex, int itemIndex) {
        double prediction = 0.0;
        for (int f = 0; f < factors; f++) {
            prediction += userFeatures[userIndex][f] * itemFeatures[itemIndex][f];
        }
        return prediction;
    }
    
    private void updateFeatures(int userIndex, int itemIndex, double error) {
        for (int f = 0; f < factors; f++) {
            double userFeature = userFeatures[userIndex][f];
            double itemFeature = itemFeatures[itemIndex][f];
            
            // 更新用户特征
            userFeatures[userIndex][f] += learningRate * (error * itemFeature - regularization * userFeature);
            
            // 更新物品特征
            itemFeatures[itemIndex][f] += learningRate * (error * userFeature - regularization * itemFeature);
        }
    }
    
    private double cosineSimilarity(double[] vectorA, double[] vectorB) {
        double dotProduct = 0.0;
        double normA = 0.0;
        double normB = 0.0;
        
        for (int i = 0; i < vectorA.length; i++) {
            dotProduct += vectorA[i] * vectorB[i];
            normA += vectorA[i] * vectorA[i];
            normB += vectorB[i] * vectorB[i];
        }
        
        if (normA == 0.0 || normB == 0.0) {
            return 0.0;
        }
        
        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }
    
    private static class Rating {
        final Long userId;
        final Long itemId;
        final double rating;
        
        Rating(Long userId, Long itemId, double rating) {
            this.userId = userId;
            this.itemId = itemId;
            this.rating = rating;
        }
    }
    
    private static class ItemScore {
        final Long itemId;
        final double score;
        
        ItemScore(Long itemId, double score) {
            this.itemId = itemId;
            this.score = score;
        }
    }
}