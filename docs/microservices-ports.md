# SpringCloud Alibaba 微服务端口配置规范

## 端口分配标准

按照SpringCloud Alibaba最佳实践，我们的微服务端口分配如下：

### 基础设施服务
- **Nacos注册中心**: `8848` (标准端口)
- **MySQL数据库**: `3306` (标准端口)

### 网关服务
- **Gateway Service**: `8090` (统一入口)

### 业务微服务
- **User Service (用户服务)**: `8081`
- **Course Service (课程服务)**: `8082` 
- **Learning Service (学习服务)**: `8083`
- **Recommendation Service (推荐服务)**: `8084`
- **Discussion Service (讨论服务)**: `8085`

### 前端服务
- **Vue.js Frontend**: `8086` (开发服务器)

## 访问规范

### 1. 前端访问后端
前端应该**统一通过网关**访问所有微服务：
```javascript
// ✅ 正确：通过网关访问
baseURL: 'http://localhost:8090'

// ❌ 错误：直接访问微服务
baseURL: 'http://localhost:8081' // 不要直接访问用户服务
baseURL: 'http://localhost:8082' // 不要直接访问课程服务
```

### 2. API路径规范
通过网关访问时，路径格式为：
```
http://localhost:8090/api/{service}/{endpoint}
```

具体示例：
- 用户登录: `POST http://localhost:8090/api/user/login`
- 课程列表: `GET http://localhost:8090/api/course/list`
- 学习仪表板: `GET http://localhost:8090/api/learning/dashboard`
- 课程推荐: `GET http://localhost:8090/api/recommendation/courses`
- 讨论列表: `GET http://localhost:8090/api/discussion/topics`

### 3. 服务间调用
微服务之间通过Nacos服务发现进行调用：
```java
// ✅ 正确：通过服务名调用
@FeignClient(name = "user-service")
public interface UserServiceClient {
    @GetMapping("/api/user/basic/{userId}")
    Result<UserInfoResponse> getUserBasicInfo(@PathVariable Long userId);
}
```

## CORS配置

网关已配置全局CORS，支持：
- 所有来源 (`allowed-origin-patterns: "*"`)
- 所有HTTP方法 (`allowed-methods: "*"`)
- 所有请求头 (`allowed-headers: "*"`)
- 携带认证信息 (`allow-credentials: true`)

## 启动顺序

推荐的服务启动顺序：
1. **Nacos** (8848)
2. **MySQL** (3306)
3. **User Service** (8081)
4. **Course Service** (8082)
5. **Learning Service** (8083)
6. **Recommendation Service** (8084)
7. **Discussion Service** (8085)
8. **Gateway Service** (8090)
9. **Frontend** (8086)

## 健康检查

所有服务都支持健康检查：
```bash
# 网关健康检查
curl http://localhost:8090/actuator/health

# 各微服务健康检查（仅用于运维，前端不应直接访问）
curl http://localhost:8081/actuator/health  # User Service
curl http://localhost:8082/actuator/health  # Course Service
curl http://localhost:8083/actuator/health  # Learning Service
curl http://localhost:8084/actuator/health  # Recommendation Service
curl http://localhost:8085/actuator/health  # Discussion Service
```

## 注意事项

1. **前端只能通过网关访问后端服务**，不允许直接访问微服务端口
2. **所有端口都是固定的**，不要随意更改
3. **开发环境和生产环境使用相同的端口规范**
4. **OPTIONS预检请求是正常的CORS行为**，不是错误
5. **JWT认证统一在网关层处理**
6. **所有API响应格式统一使用Result<T>包装**

## 故障排查

如果遇到连接问题：
1. 检查Nacos是否正常运行 (http://localhost:8848/nacos)
2. 检查各微服务是否在Nacos中正确注册
3. 检查网关路由配置是否正确
4. 检查前端API配置是否指向网关端口8090
5. 检查CORS配置是否生效
