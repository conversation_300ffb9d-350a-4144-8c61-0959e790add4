import api from '../../api/auth'

const state = {
  user: null,
  token: localStorage.getItem('token'),
  isAuthenticated: !!localStorage.getItem('token')
}

const mutations = {
  SET_USER(state, user) {
    state.user = user
  },
  SET_TOKEN(state, token) {
    state.token = token
    state.isAuthenticated = !!token
    if (token) {
      localStorage.setItem('token', token)
    } else {
      localStorage.removeItem('token')
    }
  },
  LOGOUT(state) {
    state.user = null
    state.token = null
    state.isAuthenticated = false
    localStorage.removeItem('token')
    localStorage.removeItem('userRole')
  }
}

const actions = {
  async login({ commit }, credentials) {
    try {
      const response = await api.login(credentials)
      // 后端返回的数据结构: {success: true, data: {token, userInfo}}
      const { token, userInfo } = response.data.data
      commit('SET_TOKEN', token)
      commit('SET_USER', userInfo)
      localStorage.setItem('userRole', userInfo.role)
      return response
    } catch (error) {
      throw error
    }
  },
  
  async register({ commit }, userData) {
    try {
      const response = await api.register(userData)
      return response
    } catch (error) {
      throw error
    }
  },
  
  async fetchProfile({ commit }) {
    try {
      const response = await api.getProfile()
      // API返回格式: {success: true, data: {用户信息}}
      commit('SET_USER', response.data.data)
      return response
    } catch (error) {
      throw error
    }
  },
  
  async updateProfile({ commit }, profileData) {
    try {
      const response = await api.updateProfile(profileData)
      // API返回格式: {success: true, data: {用户信息}}
      commit('SET_USER', response.data.data)
      return response
    } catch (error) {
      throw error
    }
  },
  
  logout({ commit }) {
    commit('LOGOUT')
  }
}

const getters = {
  isAuthenticated: state => state.isAuthenticated,
  user: state => state.user,
  token: state => state.token
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}