import api from '../../api/discussion'

const state = {
  discussions: [],
  currentDiscussion: null,
  replies: []
}

const mutations = {
  SET_DISCUSSIONS(state, discussions) {
    state.discussions = discussions
  },
  SET_CURRENT_DISCUSSION(state, discussion) {
    state.currentDiscussion = discussion
  },
  SET_REPLIES(state, replies) {
    state.replies = replies
  },
  ADD_DISCUSSION(state, discussion) {
    state.discussions.unshift(discussion)
  },
  ADD_REPLY(state, reply) {
    state.replies.push(reply)
  },
  UPDATE_DISCUSSION_LIKES(state, { discussionId, likesCount }) {
    const discussion = state.discussions.find(d => d.id === discussionId)
    if (discussion) {
      discussion.likesCount = likesCount
    }
    if (state.currentDiscussion && state.currentDiscussion.id === discussionId) {
      state.currentDiscussion.likesCount = likesCount
    }
  }
}

const actions = {
  async fetchDiscussions({ commit }, { courseId, page = 1, size = 10, sortBy = 'created_at' }) {
    try {
      const response = await api.getDiscussions(courseId, page, size, sortBy)
      // 正确解析API响应结构
      const discussions = response.data.data?.records || []
      commit('SET_DISCUSSIONS', discussions)
      return response
    } catch (error) {
      throw error
    }
  },

  async fetchDiscussion({ commit }, topicId) {
    try {
      const response = await api.getDiscussion(topicId)
      // 正确解析API响应结构
      const discussion = response.data.data || null
      commit('SET_CURRENT_DISCUSSION', discussion)
      return response
    } catch (error) {
      throw error
    }
  },

  async fetchReplies({ commit }, { topicId, page = 1, size = 10, sortBy = 'created_at' }) {
    try {
      const response = await api.getReplies(topicId, page, size, sortBy)
      // 正确解析API响应结构
      const replies = response.data.data?.records || []
      commit('SET_REPLIES', replies)
      return response
    } catch (error) {
      throw error
    }
  },
  
  async createDiscussion({ commit }, discussionData) {
    try {
      const response = await api.createDiscussion(discussionData)
      // 正确解析API响应结构
      const discussion = response.data.data || null
      if (discussion) {
        commit('ADD_DISCUSSION', discussion)
      }
      return response
    } catch (error) {
      throw error
    }
  },

  async createReply({ commit }, replyData) {
    try {
      const response = await api.createReply(replyData)
      // 正确解析API响应结构
      const reply = response.data.data || null
      if (reply) {
        commit('ADD_REPLY', reply)
      }
      return response
    } catch (error) {
      throw error
    }
  },
  
  async likeDiscussion({ commit }, discussionId) {
    try {
      const response = await api.likeDiscussion(discussionId)
      commit('UPDATE_DISCUSSION_LIKES', {
        discussionId,
        likesCount: response.data.likesCount
      })
      return response
    } catch (error) {
      throw error
    }
  },

  async likeReply({ commit }, replyId) {
    try {
      const response = await api.likeReply(replyId)
      return response
    } catch (error) {
      throw error
    }
  },
  
  async searchDiscussions({ commit }, { courseId, keyword }) {
    try {
      const response = await api.searchDiscussions(courseId, keyword)
      commit('SET_DISCUSSIONS', response.data)
      return response
    } catch (error) {
      throw error
    }
  }
}

const getters = {
  discussions: state => state.discussions,
  currentDiscussion: state => state.currentDiscussion,
  replies: state => state.replies
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}