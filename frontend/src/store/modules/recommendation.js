import api from '../../api/recommendation'

const state = {
  recommendations: [],
  relatedCourses: {}
}

const mutations = {
  SET_RECOMMENDATIONS(state, recommendations) {
    state.recommendations = recommendations
  },
  SET_RELATED_COURSES(state, { courseId, courses }) {
    state.relatedCourses[courseId] = courses
  }
}

const actions = {
  async fetchRecommendations({ commit }, { userId, limit = 5 }) {
    try {
      const response = await api.getRecommendations(userId, limit)
      commit('SET_RECOMMENDATIONS', response.data.data || response.data)
      return response
    } catch (error) {
      throw error
    }
  },

  async fetchRelatedCourses({ commit }, { courseId, limit = 5 }) {
    try {
      const response = await api.getRelatedCourses(courseId, limit)
      commit('SET_RELATED_COURSES', { courseId, courses: response.data.data || response.data })
      return response
    } catch (error) {
      throw error
    }
  },

  async fetchCollaborativeRecommendations({ commit }, { userId, limit = 5 }) {
    try {
      const response = await api.getCollaborativeRecommendations(userId, limit)
      commit('SET_RECOMMENDATIONS', response.data.data || response.data)
      return response
    } catch (error) {
      throw error
    }
  },

  async fetchContentBasedRecommendations({ commit }, { userId, limit = 5 }) {
    try {
      const response = await api.getContentBasedRecommendations(userId, limit)
      commit('SET_RECOMMENDATIONS', response.data.data || response.data)
      return response
    } catch (error) {
      throw error
    }
  },

  async fetchHybridRecommendations({ commit }, { userId, limit = 5 }) {
    try {
      const response = await api.getHybridRecommendations(userId, limit)
      commit('SET_RECOMMENDATIONS', response.data.data || response.data)
      return response
    } catch (error) {
      throw error
    }
  },

  async submitFeedback({ commit }, feedbackData) {
    try {
      const response = await api.submitFeedback(feedbackData)
      return response
    } catch (error) {
      throw error
    }
  }
}

const getters = {
  recommendations: state => state.recommendations,
  relatedCourses: state => state.relatedCourses,
  getRelatedCourses: state => courseId => state.relatedCourses[courseId] || []
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}