import api from '../../api/course'

const state = {
  courses: [],
  currentCourse: null,
  categories: [],
  searchResults: []
}

const mutations = {
  SET_COURSES(state, courses) {
    state.courses = courses
  },
  SET_CURRENT_COURSE(state, course) {
    state.currentCourse = course
  },
  SET_CATEGORIES(state, categories) {
    state.categories = categories
  },
  SET_SEARCH_RESULTS(state, results) {
    state.searchResults = results
  },
  ADD_COURSE(state, course) {
    state.courses.push(course)
  },
  UPDATE_COURSE(state, updatedCourse) {
    const index = state.courses.findIndex(course => course.id === updatedCourse.id)
    if (index !== -1) {
      state.courses.splice(index, 1, updatedCourse)
    }
  },
  REMOVE_COURSE(state, courseId) {
    state.courses = state.courses.filter(course => course.id !== courseId)
  }
}

const actions = {
  async fetchCourses({ commit }, params = {}) {
    try {
      const response = await api.getCourses(params)
      // 后端返回的是Result<IPage<Course>>格式，需要提取data字段
      if (response.data && response.data.success) {
        const pageData = response.data.data
        commit('SET_COURSES', pageData.records || pageData)
        return response.data
      } else {
        throw new Error(response.data?.message || '获取课程列表失败')
      }
    } catch (error) {
      throw error
    }
  },

  async fetchCourse({ commit }, courseId) {
    try {
      const response = await api.getCourse(courseId)
      if (response.data && response.data.success) {
        commit('SET_CURRENT_COURSE', response.data.data)
        return response.data
      } else {
        throw new Error(response.data?.message || '获取课程详情失败')
      }
    } catch (error) {
      throw error
    }
  },

  async searchCourses({ commit }, searchParams) {
    try {
      const response = await api.searchCourses(searchParams)
      if (response.data && response.data.success) {
        const pageData = response.data.data
        commit('SET_SEARCH_RESULTS', pageData.records || pageData)
        return response.data
      } else {
        throw new Error(response.data?.message || '搜索课程失败')
      }
    } catch (error) {
      throw error
    }
  },

  async createCourse({ commit }, courseData) {
    try {
      const response = await api.createCourse(courseData)
      if (response.data && response.data.success) {
        commit('ADD_COURSE', response.data.data)
        return response.data
      } else {
        throw new Error(response.data?.message || '创建课程失败')
      }
    } catch (error) {
      throw error
    }
  },

  async updateCourse({ commit }, { courseId, courseData }) {
    try {
      const response = await api.updateCourse(courseId, courseData)
      if (response.data && response.data.success) {
        commit('UPDATE_COURSE', response.data.data)
        return response.data
      } else {
        throw new Error(response.data?.message || '更新课程失败')
      }
    } catch (error) {
      throw error
    }
  },

  async deleteCourse({ commit }, courseId) {
    try {
      const response = await api.deleteCourse(courseId)
      if (response.data && response.data.success) {
        commit('REMOVE_COURSE', courseId)
        return response.data
      } else {
        throw new Error(response.data?.message || '删除课程失败')
      }
    } catch (error) {
      throw error
    }
  },

  async enrollCourse({ commit }, courseId) {
    try {
      const response = await api.enrollCourse(courseId)
      if (response.data && response.data.success) {
        return response.data
      } else {
        throw new Error(response.data?.message || '报名课程失败')
      }
    } catch (error) {
      throw error
    }
  },

  async fetchCategories({ commit }) {
    try {
      const response = await api.getCategories()
      if (response.data && response.data.success) {
        commit('SET_CATEGORIES', response.data.data)
        return response.data
      } else {
        throw new Error(response.data?.message || '获取课程分类失败')
      }
    } catch (error) {
      throw error
    }
  }
}

const getters = {
  courses: state => state.courses,
  currentCourse: state => state.currentCourse,
  categories: state => state.categories,
  searchResults: state => state.searchResults
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}