import api from '../../api/learning'

const state = {
  enrolledCourses: [],
  learningProgress: {},
  dashboard: null,
  quizResults: []
}

const mutations = {
  SET_ENROLLED_COURSES(state, courses) {
    state.enrolledCourses = courses
  },
  SET_LEARNING_PROGRESS(state, { courseId, progress }) {
    state.learningProgress[courseId] = progress
  },
  SET_DASHBOARD(state, dashboard) {
    state.dashboard = dashboard
  },
  ADD_QUIZ_RESULT(state, result) {
    state.quizResults.push(result)
  },
  UPDATE_PROGRESS(state, { courseId, chapterId, progress }) {
    if (!state.learningProgress[courseId]) {
      state.learningProgress[courseId] = {}
    }
    state.learningProgress[courseId][chapterId] = progress
  }
}

const actions = {
  async getDashboard({ commit }) {
    try {
      const response = await api.getDashboard()
      // API返回格式: {success: true, data: {dashboard数据}}
      commit('SET_DASHBOARD', response.data.data)
      return response.data
    } catch (error) {
      throw error
    }
  },

  async getStatistics({ commit }) {
    try {
      const response = await api.getStatistics()
      // API返回格式: {success: true, data: {统计数据}}
      return response.data
    } catch (error) {
      throw error
    }
  },
  
  async fetchLearningProgress({ commit }, courseId) {
    try {
      const response = await api.getProgress(courseId)
      commit('SET_LEARNING_PROGRESS', { courseId, progress: response.data })
      return response
    } catch (error) {
      throw error
    }
  },
  
  async updateProgress({ commit }, progressData) {
    try {
      const response = await api.updateProgress(progressData)
      commit('UPDATE_PROGRESS', {
        courseId: progressData.courseId,
        chapterId: progressData.chapterId,
        progress: response.data
      })
      return response
    } catch (error) {
      throw error
    }
  },
  
  async submitQuiz({ commit }, quizData) {
    try {
      const response = await api.submitQuiz(quizData)
      commit('ADD_QUIZ_RESULT', response.data)
      return response
    } catch (error) {
      throw error
    }
  },

  async checkEnrollment({ commit }, courseId) {
    try {
      const response = await api.checkEnrollment(courseId)
      return response
    } catch (error) {
      throw error
    }
  }

}

const getters = {
  enrolledCourses: state => state.enrolledCourses,
  learningProgress: state => state.learningProgress,
  dashboard: state => state.dashboard,
  quizResults: state => state.quizResults,
  getCourseProgress: state => courseId => state.learningProgress[courseId] || {}
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}