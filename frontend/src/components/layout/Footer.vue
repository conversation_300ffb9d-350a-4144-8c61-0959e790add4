<template>
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h3>在线学习平台</h3>
          <p>基于微服务架构的现代化学习平台，为您提供优质的在线教育体验。</p>
        </div>
        
        <div class="footer-section">
          <h4>快速链接</h4>
          <ul class="footer-links">
            <li><router-link to="/courses">课程中心</router-link></li>
            <li><router-link to="/recommendations">课程推荐</router-link></li>
            <li><router-link to="/about">关于我们</router-link></li>
            <li><router-link to="/contact">联系我们</router-link></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h4>学习支持</h4>
          <ul class="footer-links">
            <li><a href="#help">帮助中心</a></li>
            <li><a href="#faq">常见问题</a></li>
            <li><a href="#support">技术支持</a></li>
            <li><a href="#feedback">意见反馈</a></li>
          </ul>
        </div>
        

      </div>
      
      <div class="footer-bottom">
        <div class="copyright">
          <p>&copy; 2025 在线学习平台. 保留所有权利.</p>
        </div>
        <div class="footer-links-bottom">
          <a href="#privacy">隐私政策</a>
          <a href="#terms">服务条款</a>
          <a href="#cookies">Cookie政策</a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'Footer'
}
</script>

<style scoped>
.footer {
  background-color: #2c3e50;
  color: white;
  margin-top: auto;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  padding: 40px 0;
}

.footer-section h3 {
  color: #3498db;
  margin-bottom: 15px;
  font-size: 20px;
}

.footer-section h4 {
  color: #ecf0f1;
  margin-bottom: 15px;
  font-size: 16px;
}

.footer-section p {
  line-height: 1.6;
  color: #bdc3c7;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 8px;
}

.footer-links a {
  color: #bdc3c7;
  text-decoration: none;
  transition: color 0.3s;
}

.footer-links a:hover {
  color: #3498db;
}

.contact-info p {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  padding: 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.copyright p {
  margin: 0;
  color: #bdc3c7;
}

.footer-links-bottom {
  display: flex;
  gap: 20px;
}

.footer-links-bottom a {
  color: #bdc3c7;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.footer-links-bottom a:hover {
  color: #3498db;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
  
  .footer-links-bottom {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style>