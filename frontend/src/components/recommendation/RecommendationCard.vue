<template>
  <div class="recommendation-card" :class="{ 'loading': !course }">
    <!-- 加载状态 -->
    <div v-if="!course" class="card-loading">
      <div class="loading-placeholder">
        <div class="placeholder-image"></div>
        <div class="placeholder-content">
          <div class="placeholder-line long"></div>
          <div class="placeholder-line medium"></div>
          <div class="placeholder-line short"></div>
        </div>
      </div>
    </div>

    <!-- 课程内容 -->
    <div v-else class="card-content" @click="$emit('view-course', course.id)">
      <!-- 课程封面 -->
      <div class="course-image">
        <img 
          :src="course.coverImage || '/placeholder-course.jpg'" 
          :alt="course.title"
          @error="handleImageError"
        >
        <div class="course-level">{{ getLevelText(course.difficultyLevel) }}</div>
        <div class="recommendation-score">
          <span class="score-icon">⭐</span>
          <span class="score-value">{{ formatScore(recommendation.score) }}</span>
        </div>
      </div>

      <!-- 课程信息 -->
      <div class="course-info">
        <h3 class="course-title">{{ course.title }}</h3>
        <p class="course-description">{{ truncateText(course.description, 100) }}</p>
        
        <div class="course-meta">
          <div class="meta-item">
            <span class="meta-icon">👨‍🏫</span>
            <span class="meta-text">{{ course.teacherName || '未知讲师' }}</span>
          </div>
          <div class="meta-item">
            <span class="meta-icon">👥</span>
            <span class="meta-text">{{ course.studentsCount || 0 }}人学习</span>
          </div>
          <div class="meta-item" v-if="course.price">
            <span class="meta-icon">💰</span>
            <span class="meta-text">
              {{ course.price > 0 ? `¥${course.price}` : '免费' }}
            </span>
          </div>
        </div>

        <!-- 推荐原因 -->
        <div class="recommendation-reason">
          <span class="reason-icon">💡</span>
          <span class="reason-text">{{ recommendation.reason || '系统推荐' }}</span>
        </div>
      </div>

      <!-- 反馈按钮 -->
      <div class="feedback-actions">
        <button 
          class="feedback-btn like-btn"
          :class="{ active: userFeedback === 'LIKE' }"
          @click.stop="handleFeedback('LIKE')"
          title="喜欢这个推荐"
        >
          <span class="btn-icon">👍</span>
          <span class="btn-text">喜欢</span>
        </button>
        <button 
          class="feedback-btn dislike-btn"
          :class="{ active: userFeedback === 'DISLIKE' }"
          @click.stop="handleFeedback('DISLIKE')"
          title="不喜欢这个推荐"
        >
          <span class="btn-icon">👎</span>
          <span class="btn-text">不喜欢</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'RecommendationCard',
  props: {
    recommendation: {
      type: Object,
      required: true
    },
    course: {
      type: Object,
      default: null
    }
  },
  emits: ['feedback', 'view-course'],
  setup(props, { emit }) {
    const userFeedback = ref(null)
    
    const getLevelText = (level) => {
      const levelMap = {
        'BEGINNER': '初级',
        'INTERMEDIATE': '中级',
        'ADVANCED': '高级'
      }
      return levelMap[level] || '未知'
    }
    
    const formatScore = (score) => {
      if (typeof score === 'number') {
        return (score * 100).toFixed(0) + '%'
      }
      return '0%'
    }
    
    const truncateText = (text, maxLength) => {
      if (!text) return ''
      if (text.length <= maxLength) return text
      return text.substring(0, maxLength) + '...'
    }
    
    const handleImageError = (event) => {
      event.target.src = '/placeholder-course.jpg'
    }
    
    const handleFeedback = (type) => {
      userFeedback.value = userFeedback.value === type ? null : type
      emit('feedback', {
        courseId: props.course.id,
        type: type
      })
    }
    
    return {
      userFeedback,
      getLevelText,
      formatScore,
      truncateText,
      handleImageError,
      handleFeedback
    }
  }
}
</script>

<style scoped>
.recommendation-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.recommendation-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 加载状态 */
.card-loading {
  padding: 20px;
}

.loading-placeholder {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.placeholder-image {
  width: 100%;
  height: 200px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8px;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.placeholder-line {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

.placeholder-line.long { width: 100%; }
.placeholder-line.medium { width: 75%; }
.placeholder-line.short { width: 50%; }

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* 课程内容 */
.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.course-image {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.recommendation-card:hover .course-image img {
  transform: scale(1.05);
}

.course-level {
  position: absolute;
  top: 12px;
  left: 12px;
  background: rgba(52, 152, 219, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.recommendation-score {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 193, 7, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.score-icon {
  font-size: 0.7rem;
}

/* 课程信息 */
.course-info {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.course-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  line-height: 1.4;
}

.course-description {
  color: #7f8c8d;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}

.course-meta {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
  color: #95a5a6;
}

.meta-icon {
  font-size: 0.8rem;
}

.recommendation-reason {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: auto;
}

.reason-icon {
  font-size: 0.9rem;
}

/* 反馈按钮 */
.feedback-actions {
  display: flex;
  border-top: 1px solid #e9ecef;
}

.feedback-btn {
  flex: 1;
  background: none;
  border: none;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  color: #6c757d;
}

.feedback-btn:hover {
  background: #f8f9fa;
}

.feedback-btn.active.like-btn {
  background: #d4edda;
  color: #155724;
}

.feedback-btn.active.dislike-btn {
  background: #f8d7da;
  color: #721c24;
}

.feedback-btn + .feedback-btn {
  border-left: 1px solid #e9ecef;
}

.btn-icon {
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .course-info {
    padding: 15px;
  }
  
  .course-title {
    font-size: 1.1rem;
  }
  
  .course-meta {
    gap: 4px;
  }
  
  .meta-item {
    font-size: 0.8rem;
  }
}
</style>
