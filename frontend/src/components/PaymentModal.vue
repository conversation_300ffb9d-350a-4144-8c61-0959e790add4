<template>
  <div v-if="visible" class="payment-modal-overlay" @click="closeModal">
    <div class="payment-modal" @click.stop>
      <div class="payment-header">
        <h3>课程支付</h3>
        <button class="close-btn" @click="closeModal">&times;</button>
      </div>
      
      <div class="payment-content">
        <div class="course-info">
          <img :src="course.coverImage" :alt="course.title" class="course-cover">
          <div class="course-details">
            <h4>{{ course.title }}</h4>
            <p class="course-teacher">讲师：{{ course.teacherName }}</p>
            <p class="course-price">￥{{ course.price }}</p>
          </div>
        </div>
        
        <div class="payment-methods">
          <h4>选择支付方式</h4>
          <div class="payment-options">
            <label class="payment-option">
              <input type="radio" v-model="selectedPaymentMethod" value="alipay">
              <span class="payment-icon">💰</span>
              <span>支付宝</span>
            </label>
            <label class="payment-option">
              <input type="radio" v-model="selectedPaymentMethod" value="wechat">
              <span class="payment-icon">💚</span>
              <span>微信支付</span>
            </label>
            <label class="payment-option">
              <input type="radio" v-model="selectedPaymentMethod" value="card">
              <span class="payment-icon">💳</span>
              <span>银行卡</span>
            </label>
          </div>
        </div>
        
        <div class="simulation-controls">
          <h4>模拟支付结果</h4>
          <div class="simulation-options">
            <label class="simulation-option">
              <input type="radio" v-model="simulationResult" value="success">
              <span class="result-icon success">✅</span>
              <span>支付成功</span>
            </label>
            <label class="simulation-option">
              <input type="radio" v-model="simulationResult" value="failure">
              <span class="result-icon failure">❌</span>
              <span>支付失败</span>
            </label>
          </div>
        </div>
        
        <div class="payment-summary">
          <div class="summary-row">
            <span>课程价格：</span>
            <span>￥{{ course.price }}</span>
          </div>
          <div class="summary-row">
            <span>优惠折扣：</span>
            <span>-￥0.00</span>
          </div>
          <div class="summary-row total">
            <span>实付金额：</span>
            <span>￥{{ course.price }}</span>
          </div>
        </div>
      </div>
      
      <div class="payment-actions">
        <button class="btn btn-secondary" @click="closeModal">取消</button>
        <button 
          class="btn btn-primary" 
          @click="processPayment"
          :disabled="processing || !selectedPaymentMethod || !simulationResult"
        >
          <span v-if="processing">处理中...</span>
          <span v-else>确认支付 ￥{{ course.price }}</span>
        </button>
      </div>
      
      <!-- 支付处理中的遮罩 -->
      <div v-if="processing" class="processing-overlay">
        <div class="processing-content">
          <div class="spinner"></div>
          <p>正在处理支付...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import paymentApi from '@/api/payment'

export default {
  name: 'PaymentModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    course: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'success', 'failure'],
  setup(props, { emit }) {
    const selectedPaymentMethod = ref('')
    const simulationResult = ref('success')
    const processing = ref(false)
    
    const closeModal = () => {
      emit('close')
    }
    
    const processPayment = async () => {
      if (!selectedPaymentMethod.value || !simulationResult.value) {
        return
      }
      
      processing.value = true
      
      try {
        // 模拟支付处理时间
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        const paymentData = {
          courseId: props.course.id,
          amount: props.course.price,
          paymentMethod: selectedPaymentMethod.value,
          simulateResult: simulationResult.value
        }
        
        const response = await paymentApi.simulatePayment(paymentData)
        
        if (response.data.success && simulationResult.value === 'success') {
          emit('success', response.data)
        } else {
          emit('failure', response.data)
        }
        
      } catch (error) {
        console.error('支付处理失败:', error)
        emit('failure', { message: '支付处理失败，请重试' })
      } finally {
        processing.value = false
      }
    }
    
    return {
      selectedPaymentMethod,
      simulationResult,
      processing,
      closeModal,
      processPayment
    }
  }
}
</script>

<style scoped>
.payment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.payment-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.payment-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #7f8c8d;
}

.payment-content {
  padding: 20px;
}

.course-info {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.course-cover {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
}

.course-details h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.course-teacher {
  margin: 0 0 5px 0;
  color: #7f8c8d;
  font-size: 14px;
}

.course-price {
  margin: 0;
  color: #e74c3c;
  font-size: 18px;
  font-weight: bold;
}

.payment-methods, .simulation-controls {
  margin-bottom: 25px;
}

.payment-methods h4, .simulation-controls h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.payment-options, .simulation-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.payment-option, .simulation-option {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.payment-option:hover, .simulation-option:hover {
  border-color: #3498db;
  background-color: #f8f9fa;
}

.payment-option input:checked + .payment-icon + span,
.simulation-option input:checked + .result-icon + span {
  color: #3498db;
  font-weight: bold;
}

.payment-option input[type="radio"]:checked ~ *,
.simulation-option input[type="radio"]:checked ~ * {
  border-color: #3498db;
}

.payment-icon, .result-icon {
  font-size: 20px;
}

.result-icon.success {
  color: #27ae60;
}

.result-icon.failure {
  color: #e74c3c;
}

.payment-summary {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.summary-row.total {
  border-top: 1px solid #ddd;
  padding-top: 8px;
  font-weight: bold;
  color: #e74c3c;
}

.payment-actions {
  display: flex;
  gap: 15px;
  padding: 20px;
  border-top: 1px solid #eee;
}

.btn {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.btn-secondary {
  background-color: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background-color: #7f8c8d;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2980b9;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
}

.processing-content {
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .payment-modal {
    width: 95%;
    margin: 20px;
  }
  
  .course-info {
    flex-direction: column;
    text-align: center;
  }
  
  .course-cover {
    align-self: center;
  }
}
</style>
