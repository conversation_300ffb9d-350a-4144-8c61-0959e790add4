import api from './index'

export default {
  // 获取课程推荐
  getRecommendations(userId, limit = 5) {
    return api.get('/api/recommendation/courses', {
      params: { userId, limit }
    })
  },

  // 获取相关课程推荐
  getRelatedCourses(courseId, limit = 5) {
    return api.get(`/api/recommendation/related/${courseId}`, {
      params: { limit }
    })
  },

  // 获取协同过滤推荐
  getCollaborativeRecommendations(userId, limit = 5) {
    return api.get('/api/recommendation/collaborative', {
      params: { userId, limit }
    })
  },

  // 获取内容过滤推荐
  getContentBasedRecommendations(userId, limit = 5) {
    return api.get('/api/recommendation/content-based', {
      params: { userId, limit }
    })
  },

  // 获取混合推荐
  getHybridRecommendations(userId, limit = 5) {
    return api.get('/api/recommendation/hybrid', {
      params: { userId, limit }
    })
  },

  // 提交推荐反馈
  submitFeedback(feedbackData) {
    return api.post('/api/recommendation/feedback', feedbackData)
  }
}