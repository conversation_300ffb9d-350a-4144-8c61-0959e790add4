import api from './index'

export default {
  // 获取课程讨论列表
  getDiscussions(courseId, page = 1, size = 10, sortBy = 'created_at') {
    return api.get(`/api/discussion/courses/${courseId}/topics`, {
      params: { page, size, sortBy }
    })
  },

  // 获取讨论详情
  getDiscussion(topicId) {
    return api.get(`/api/discussion/topics/${topicId}`)
  },

  // 获取讨论回复
  getReplies(topicId, page = 1, size = 10, sortBy = 'created_at') {
    return api.get(`/api/discussion/topics/${topicId}/replies`, {
      params: { page, size, sortBy }
    })
  },

  // 创建讨论
  createDiscussion(discussionData) {
    return api.post('/api/discussion/topics', discussionData)
  },

  // 创建回复
  createReply(replyData) {
    return api.post(`/api/discussion/topics/${replyData.topicId}/replies`, replyData)
  },

  // 点赞讨论
  likeDiscussion(topicId) {
    return api.put(`/api/discussion/topics/${topicId}/like`)
  },

  // 点赞回复
  likeReply(replyId) {
    return api.post(`/api/discussion/replies/${replyId}/like`)
  },

  // 搜索讨论
  searchDiscussions(courseId, keyword, page = 1, size = 10) {
    return api.get(`/api/discussion/courses/${courseId}/topics/search`, {
      params: { keyword, page, size }
    })
  },

  // 获取热门讨论
  getHotDiscussions(courseId, page = 1, size = 10) {
    return api.get(`/api/discussion/courses/${courseId}/topics/hot`, {
      params: { page, size }
    })
  }
}