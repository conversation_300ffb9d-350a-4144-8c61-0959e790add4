import api from './index'

export default {
  // 用户登录
  login(credentials) {
    // 转换字段名以匹配后端API
    const loginData = {
      usernameOrEmail: credentials.username,
      password: credentials.password
    }
    return api.post('/api/user/login', loginData)
  },
  
  // 用户注册
  register(userData) {
    return api.post('/api/user/register', userData)
  },
  
  // 获取用户信息
  getProfile() {
    return api.get('/api/user/profile')
  },
  
  // 更新用户信息
  updateProfile(profileData) {
    return api.put('/api/user/profile', profileData)
  },
  
  // 重置密码
  resetPassword(email) {
    return api.post('/api/user/reset-password', { email })
  },
  
  // 邮箱验证
  verifyEmail(token) {
    return api.get(`/api/user/verify-email?token=${token}`)
  }
}