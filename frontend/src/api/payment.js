import api from './index'

export default {
  // 创建支付订单
  createPaymentOrder(orderData) {
    return api.post('/api/payment/create', orderData)
  },
  
  // 模拟支付
  simulatePayment(paymentData) {
    return api.post('/api/payment/simulate', paymentData)
  },
  
  // 查询支付状态
  getPaymentStatus(orderId) {
    return api.get(`/api/payment/status/${orderId}`)
  },
  
  // 获取用户支付记录
  getPaymentHistory() {
    return api.get('/api/payment/history')
  }
}
