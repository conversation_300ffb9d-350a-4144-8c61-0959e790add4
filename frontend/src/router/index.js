import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/auth/Login.vue')
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/auth/Register.vue')
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../views/profile/Profile.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/courses',
    name: 'CourseList',
    component: () => import('../views/course/CourseList.vue')
  },
  {
    path: '/course/:id',
    name: 'CourseDetail',
    component: () => import('../views/course/CourseDetail.vue')
  },
  {
    path: '/learning',
    name: 'LearningDashboard',
    component: () => import('../views/learning/LearningDashboard.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/learning/course/:id',
    name: 'CourseLearning',
    component: () => import('../views/learning/CourseLearning.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/learning/statistics',
    name: 'LearningStatistics',
    component: () => import('../views/learning/LearningStatistics.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/recommendations',
    name: 'Recommendations',
    component: () => import('../views/recommendation/Recommendations.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/discussion/:courseId',
    name: 'Discussion',
    component: () => import('../views/discussion/Discussion.vue')
  },
  {
    path: '/course/manage',
    name: 'CourseManage',
    component: () => import('../views/course/CourseManage.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/course/create',
    name: 'CourseCreate',
    component: () => import('../views/course/CourseEdit.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/course/edit/:id',
    name: 'CourseEdit',
    component: () => import('../views/course/CourseEdit.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/teacher/courses',
    name: 'TeacherCourses',
    component: () => import('../views/teacher/TeacherCourses.vue'),
    meta: { requiresAuth: true, requiresRole: 'TEACHER' }
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  const userRole = localStorage.getItem('userRole')
  
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!token) {
      next('/login')
    } else if (to.meta.requiresRole && userRole !== to.meta.requiresRole) {
      next('/')
    } else {
      next()
    }
  } else {
    next()
  }
})

export default router