<template>
  <div class="recommendations">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">
          <span class="icon">🎯</span>
          个性化课程推荐
        </h1>
        <p class="page-subtitle">基于先进的推荐算法，为您精选最适合的学习内容</p>
      </div>

      <!-- 推荐算法选择器 -->
      <div class="algorithm-selector">
        <h3>推荐算法</h3>
        <div class="algorithm-tabs">
          <button
            v-for="algorithm in algorithms"
            :key="algorithm.key"
            :class="['algorithm-tab', { active: selectedAlgorithm === algorithm.key }]"
            @click="selectAlgorithm(algorithm.key)"
          >
            <span class="tab-icon">{{ algorithm.icon }}</span>
            <span class="tab-label">{{ algorithm.label }}</span>
            <span class="tab-description">{{ algorithm.description }}</span>
          </button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>正在为您生成个性化推荐...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <div class="error-icon">⚠️</div>
        <h3>推荐加载失败</h3>
        <p>{{ error }}</p>
        <button class="btn btn-primary" @click="fetchRecommendations">重新加载</button>
      </div>

      <!-- 推荐结果 -->
      <div v-else-if="recommendations.length > 0" class="recommendations-section">
        <div class="section-header">
          <h3>
            <span class="algorithm-icon">{{ getCurrentAlgorithmIcon() }}</span>
            {{ getCurrentAlgorithmName() }}推荐结果
          </h3>
          <p class="algorithm-info">{{ getCurrentAlgorithmInfo() }}</p>
        </div>

        <div class="recommendations-grid">
          <RecommendationCard
            v-for="recommendation in recommendations"
            :key="`${recommendation.courseId}-${selectedAlgorithm}`"
            :recommendation="recommendation"
            :course="getCourseInfo(recommendation.courseId)"
            @feedback="handleFeedback"
            @view-course="viewCourse"
          />
        </div>

        <!-- 刷新推荐按钮 -->
        <div class="refresh-section">
          <button
            class="btn btn-outline refresh-btn"
            @click="refreshRecommendations"
            :disabled="refreshing"
          >
            <span class="refresh-icon" :class="{ spinning: refreshing }">🔄</span>
            {{ refreshing ? '正在刷新...' : '刷新推荐' }}
          </button>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-icon">📚</div>
        <h3>暂无推荐内容</h3>
        <p>系统正在学习您的偏好，请先浏览一些课程来获得更好的推荐</p>
        <router-link to="/courses" class="btn btn-primary">浏览课程</router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, onMounted, ref, watch } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import RecommendationCard from '../../components/recommendation/RecommendationCard.vue'
import courseApi from '../../api/course'

export default {
  name: 'RecommendationsPage',
  components: {
    RecommendationCard
  },
  setup() {
    const store = useStore()
    const router = useRouter()

    // 响应式数据
    const loading = ref(false)
    const refreshing = ref(false)
    const error = ref(null)
    const selectedAlgorithm = ref('hybrid')
    const recommendations = ref([])
    const courseInfoCache = ref({})

    // 推荐算法配置
    const algorithms = ref([
      {
        key: 'hybrid',
        label: '智能推荐',
        icon: '🧠',
        description: '结合多种算法的混合推荐',
        info: '综合协同过滤、内容过滤和TF-IDF算法，为您提供最精准的推荐'
      },
      {
        key: 'collaborative',
        label: '协同过滤',
        icon: '👥',
        description: '基于相似用户的推荐',
        info: '分析与您学习偏好相似的用户，推荐他们喜欢的课程'
      },
      {
        key: 'content',
        label: '内容过滤',
        icon: '🔍',
        description: '基于课程内容的推荐',
        info: '根据您已学习课程的特征，推荐相似内容的课程'
      }
    ])

    // 计算属性
    const user = computed(() => store.getters['auth/user'])
    const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])

    // 获取当前算法信息
    const getCurrentAlgorithmIcon = () => {
      const algorithm = algorithms.value.find(alg => alg.key === selectedAlgorithm.value)
      return algorithm ? algorithm.icon : '🎯'
    }

    const getCurrentAlgorithmName = () => {
      const algorithm = algorithms.value.find(alg => alg.key === selectedAlgorithm.value)
      return algorithm ? algorithm.label : '推荐'
    }

    const getCurrentAlgorithmInfo = () => {
      const algorithm = algorithms.value.find(alg => alg.key === selectedAlgorithm.value)
      return algorithm ? algorithm.info : ''
    }

    // 方法
    const selectAlgorithm = (algorithmKey) => {
      if (selectedAlgorithm.value !== algorithmKey) {
        selectedAlgorithm.value = algorithmKey
        fetchRecommendations()
      }
    }

    const fetchRecommendations = async () => {
      if (!isAuthenticated.value || !user.value?.id) {
        error.value = '请先登录以获取个性化推荐'
        return
      }

      loading.value = true
      error.value = null

      try {
        let response
        const userId = user.value.id
        const limit = 12

        switch (selectedAlgorithm.value) {
          case 'collaborative':
            response = await store.dispatch('recommendation/fetchCollaborativeRecommendations', { userId, limit })
            break
          case 'content':
            response = await store.dispatch('recommendation/fetchContentBasedRecommendations', { userId, limit })
            break
          case 'hybrid':
          default:
            response = await store.dispatch('recommendation/fetchHybridRecommendations', { userId, limit })
            break
        }

        recommendations.value = response.data.data || response.data || []

        // 预加载课程信息
        await preloadCourseInfo()

      } catch (err) {
        console.error('获取推荐失败:', err)
        error.value = err.response?.data?.message || '获取推荐失败，请稍后重试'
      } finally {
        loading.value = false
      }
    }

    const refreshRecommendations = async () => {
      refreshing.value = true
      await fetchRecommendations()
      refreshing.value = false
    }

    const preloadCourseInfo = async () => {
      const courseIds = recommendations.value.map(rec => rec.courseId)
      const uniqueCourseIds = [...new Set(courseIds)]

      for (const courseId of uniqueCourseIds) {
        if (!courseInfoCache.value[courseId]) {
          try {
            const response = await courseApi.getCourse(courseId)
            if (response.data.success) {
              courseInfoCache.value[courseId] = response.data.data
            }
          } catch (error) {
            console.error(`获取课程${courseId}信息失败:`, error)
          }
        }
      }
    }

    const getCourseInfo = (courseId) => {
      return courseInfoCache.value[courseId] || null
    }

    const handleFeedback = async (feedbackData) => {
      try {
        await store.dispatch('recommendation/submitFeedback', {
          userId: user.value.id,
          courseId: feedbackData.courseId,
          feedback: feedbackData.type
        })

        // 可以显示反馈成功的提示
        console.log('反馈提交成功')
      } catch (error) {
        console.error('提交反馈失败:', error)
      }
    }

    const viewCourse = (courseId) => {
      // 记录点击行为
      handleFeedback({ courseId, type: 'CLICK' })
      // 跳转到课程详情页
      router.push(`/course/${courseId}`)
    }

    // 监听算法切换
    watch(selectedAlgorithm, () => {
      if (isAuthenticated.value) {
        fetchRecommendations()
      }
    })

    // 组件挂载时获取推荐
    onMounted(() => {
      if (isAuthenticated.value) {
        fetchRecommendations()
      } else {
        error.value = '请先登录以获取个性化推荐'
      }
    })

    return {
      loading,
      refreshing,
      error,
      selectedAlgorithm,
      recommendations,
      courseInfoCache,
      algorithms,
      user,
      isAuthenticated,
      getCurrentAlgorithmIcon,
      getCurrentAlgorithmName,
      getCurrentAlgorithmInfo,
      selectAlgorithm,
      fetchRecommendations,
      refreshRecommendations,
      getCourseInfo,
      handleFeedback,
      viewCourse
    }
  }
}
</script>

<style scoped>
.recommendations {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.page-title .icon {
  font-size: 2.2rem;
}

.page-subtitle {
  font-size: 1.1rem;
  color: #7f8c8d;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* 算法选择器 */
.algorithm-selector {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.algorithm-selector h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.algorithm-tabs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 15px;
}

.algorithm-tab {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.algorithm-tab:hover {
  border-color: #3498db;
  background: #f0f8ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.15);
}

.algorithm-tab.active {
  border-color: #3498db;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
}

.tab-icon {
  font-size: 1.8rem;
  margin-bottom: 5px;
}

.tab-label {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.tab-description {
  font-size: 0.9rem;
  opacity: 0.8;
  line-height: 1.4;
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-container {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.error-container h3 {
  color: #e74c3c;
  margin-bottom: 10px;
}

.error-container p {
  color: #7f8c8d;
  margin-bottom: 25px;
}

/* 推荐结果区域 */
.recommendations-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.section-header {
  margin-bottom: 30px;
  text-align: center;
}

.section-header h3 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.algorithm-icon {
  font-size: 1.3rem;
}

.algorithm-info {
  color: #7f8c8d;
  font-size: 1rem;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.5;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

/* 刷新按钮 */
.refresh-section {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.refresh-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  font-size: 1rem;
}

.refresh-icon {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 25px;
}

.empty-state h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.4rem;
}

.empty-state p {
  color: #7f8c8d;
  margin-bottom: 30px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 10px;
  }

  .algorithm-tabs {
    grid-template-columns: 1fr;
  }

  .recommendations-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .algorithm-tab {
    padding: 15px;
  }

  .recommendations-section {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .page-title {
    font-size: 1.8rem;
  }

  .algorithm-selector {
    padding: 20px;
  }
}
</style>