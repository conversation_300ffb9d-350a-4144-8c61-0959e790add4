<template>
  <div class="course-edit-page">
    <div class="container">
      <div class="page-header">
        <div class="breadcrumb">
          <router-link to="/course/manage">我的课程</router-link>
          <span> / </span>
          <span>{{ isEdit ? '编辑课程' : '创建课程' }}</span>
        </div>
        <h1>{{ isEdit ? '编辑课程' : '创建新课程' }}</h1>
      </div>
      
      <form @submit.prevent="handleSubmit" class="course-form">
        <!-- 基本信息 -->
        <div class="form-section">
          <h2 class="section-title">基本信息</h2>
          
          <div class="form-group">
            <label for="title" class="form-label">课程标题 *</label>
            <input
              id="title"
              v-model="courseData.title"
              type="text"
              class="form-control"
              placeholder="请输入课程标题"
              required
              maxlength="200"
            >
            <div class="form-help">{{ courseData.title.length }}/200</div>
          </div>
          
          <div class="form-group">
            <label for="description" class="form-label">课程描述 *</label>
            <textarea
              id="description"
              v-model="courseData.description"
              class="form-control"
              rows="4"
              placeholder="请输入课程描述"
              required
              maxlength="1000"
            ></textarea>
            <div class="form-help">{{ courseData.description.length }}/1000</div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="category" class="form-label">课程分类 *</label>
              <select
                id="category"
                v-model="courseData.categoryId"
                class="form-control"
                required
              >
                <option value="">请选择分类</option>
                <option value="1">编程开发</option>
                <option value="2">设计创意</option>
                <option value="3">商业管理</option>
                <option value="4">语言学习</option>
                <option value="5">职业技能</option>
              </select>
            </div>
            
            <div class="form-group">
              <label for="difficulty" class="form-label">难度级别 *</label>
              <select
                id="difficulty"
                v-model="courseData.difficultyLevel"
                class="form-control"
                required
              >
                <option value="">请选择难度</option>
                <option value="BEGINNER">初级</option>
                <option value="INTERMEDIATE">中级</option>
                <option value="ADVANCED">高级</option>
              </select>
            </div>
            
            <div class="form-group">
              <label for="price" class="form-label">课程价格</label>
              <input
                id="price"
                v-model.number="courseData.price"
                type="number"
                class="form-control"
                placeholder="0"
                min="0"
                step="0.01"
              >
              <div class="form-help">设置为0表示免费课程</div>
            </div>
          </div>
          
          <div class="form-group">
            <label for="coverImage" class="form-label">课程封面</label>
            <div class="image-upload">
              <div v-if="courseData.coverImage" class="image-preview">
                <img :src="courseData.coverImage" alt="课程封面">
                <button type="button" class="remove-image" @click="removeCoverImage">
                  ✕
                </button>
              </div>
              <div v-else class="upload-placeholder">
                <input
                  ref="fileInput"
                  type="file"
                  accept="image/*"
                  @change="handleImageUpload"
                  style="display: none"
                >
                <button type="button" class="upload-btn" @click="$refs.fileInput.click()">
                  📷 上传封面图片
                </button>
                <div class="upload-help">建议尺寸：800x450px，支持JPG、PNG格式</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 课程章节 -->
        <div class="form-section">
          <div class="section-header">
            <h2 class="section-title">课程章节</h2>
            <button type="button" class="btn btn-outline" @click="addChapter">
              ➕ 添加章节
            </button>
          </div>
          
          <div v-if="courseData.chapters.length === 0" class="empty-chapters">
            <p>还没有添加章节，点击上方按钮开始添加</p>
          </div>
          
          <div v-else class="chapters-list">
            <div 
              v-for="(chapter, index) in courseData.chapters" 
              :key="chapter.tempId || chapter.id"
              class="chapter-item"
            >
              <div class="chapter-header">
                <div class="chapter-number">{{ index + 1 }}</div>
                <div class="chapter-controls">
                  <button 
                    type="button" 
                    class="btn btn-sm btn-outline"
                    @click="moveChapter(index, -1)"
                    :disabled="index === 0"
                  >
                    ↑
                  </button>
                  <button 
                    type="button" 
                    class="btn btn-sm btn-outline"
                    @click="moveChapter(index, 1)"
                    :disabled="index === courseData.chapters.length - 1"
                  >
                    ↓
                  </button>
                  <button 
                    type="button" 
                    class="btn btn-sm btn-danger"
                    @click="removeChapter(index)"
                  >
                    删除
                  </button>
                </div>
              </div>
              
              <div class="chapter-content">
                <div class="form-group">
                  <label class="form-label">章节标题 *</label>
                  <input
                    v-model="chapter.title"
                    type="text"
                    class="form-control"
                    placeholder="请输入章节标题"
                    required
                    maxlength="200"
                  >
                </div>
                
                <div class="form-row">
                  <div class="form-group">
                    <label class="form-label">内容类型 *</label>
                    <select
                      v-model="chapter.contentType"
                      class="form-control"
                      required
                    >
                      <option value="">请选择类型</option>
                      <option value="VIDEO">视频</option>
                      <option value="DOCUMENT">文档</option>
                      <option value="QUIZ">测验</option>
                    </select>
                  </div>
                  
                  <div class="form-group">
                    <label class="form-label">时长（分钟）</label>
                    <input
                      v-model.number="chapter.durationMinutes"
                      type="number"
                      class="form-control"
                      placeholder="0"
                      min="0"
                    >
                  </div>
                </div>
                
                <div class="form-group">
                  <label class="form-label">内容链接</label>
                  <input
                    v-model="chapter.contentUrl"
                    type="url"
                    class="form-control"
                    placeholder="请输入内容链接（视频链接、文档链接等）"
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 发布设置 -->
        <div class="form-section">
          <h2 class="section-title">发布设置</h2>
          
          <div class="form-group">
            <label class="form-label">课程状态</label>
            <div class="radio-group">
              <label class="radio-item">
                <input
                  v-model="courseData.status"
                  type="radio"
                  value="DRAFT"
                >
                <span class="radio-label">保存为草稿</span>
                <span class="radio-help">课程将保存但不会对学生可见</span>
              </label>
              <label class="radio-item">
                <input
                  v-model="courseData.status"
                  type="radio"
                  value="PUBLISHED"
                >
                <span class="radio-label">立即发布</span>
                <span class="radio-help">课程将立即对学生可见</span>
              </label>
            </div>
          </div>
        </div>
        
        <!-- 提交按钮 -->
        <div class="form-actions">
          <router-link to="/course/manage" class="btn btn-outline">
            取消
          </router-link>
          <button type="submit" class="btn btn-primary" :disabled="submitting">
            <span v-if="submitting">{{ isEdit ? '更新中...' : '创建中...' }}</span>
            <span v-else>{{ isEdit ? '更新课程' : '创建课程' }}</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'

export default {
  name: 'CourseEdit',
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()
    
    const submitting = ref(false)
    const fileInput = ref(null)
    
    const courseId = computed(() => route.params.id)
    const isEdit = computed(() => !!courseId.value)
    
    const courseData = ref({
      title: '',
      description: '',
      categoryId: '',
      difficultyLevel: '',
      price: 0,
      coverImage: '',
      status: 'DRAFT',
      chapters: []
    })
    
    const generateTempId = () => {
      return 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    }
    
    const addChapter = () => {
      courseData.value.chapters.push({
        tempId: generateTempId(),
        title: '',
        contentType: '',
        contentUrl: '',
        durationMinutes: 0,
        orderIndex: courseData.value.chapters.length
      })
    }
    
    const removeChapter = (index) => {
      if (confirm('确定要删除这个章节吗？')) {
        courseData.value.chapters.splice(index, 1)
        // 重新设置排序索引
        courseData.value.chapters.forEach((chapter, idx) => {
          chapter.orderIndex = idx
        })
      }
    }
    
    const moveChapter = (index, direction) => {
      const newIndex = index + direction
      if (newIndex >= 0 && newIndex < courseData.value.chapters.length) {
        const chapters = courseData.value.chapters
        const temp = chapters[index]
        chapters[index] = chapters[newIndex]
        chapters[newIndex] = temp
        
        // 重新设置排序索引
        chapters.forEach((chapter, idx) => {
          chapter.orderIndex = idx
        })
      }
    }
    
    const handleImageUpload = (event) => {
      const file = event.target.files[0]
      if (!file) return
      
      // 检查文件类型
      if (!file.type.startsWith('image/')) {
        alert('请选择图片文件')
        return
      }
      
      // 检查文件大小（限制为5MB）
      if (file.size > 5 * 1024 * 1024) {
        alert('图片文件大小不能超过5MB')
        return
      }
      
      // 创建预览
      const reader = new FileReader()
      reader.onload = (e) => {
        courseData.value.coverImage = e.target.result
      }
      reader.readAsDataURL(file)
    }
    
    const removeCoverImage = () => {
      courseData.value.coverImage = ''
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    }
    
    const loadCourseData = async () => {
      if (!isEdit.value) return
      
      try {
        const response = await store.dispatch('course/fetchCourse', courseId.value)
        const course = response.data
        
        courseData.value = {
          title: course.title || '',
          description: course.description || '',
          categoryId: course.categoryId || '',
          difficultyLevel: course.difficultyLevel || '',
          price: course.price || 0,
          coverImage: course.coverImage || '',
          status: course.status || 'DRAFT',
          chapters: course.chapters || []
        }
        
        // 为现有章节添加临时ID（如果没有的话）
        courseData.value.chapters.forEach(chapter => {
          if (!chapter.tempId && !chapter.id) {
            chapter.tempId = generateTempId()
          }
        })
        
      } catch (error) {
        console.error('加载课程数据失败:', error)
        store.dispatch('setError', '加载课程数据失败，请稍后重试')
        router.push('/course/manage')
      }
    }
    
    const validateForm = () => {
      if (!courseData.value.title.trim()) {
        alert('请输入课程标题')
        return false
      }
      
      if (!courseData.value.description.trim()) {
        alert('请输入课程描述')
        return false
      }
      
      if (!courseData.value.categoryId) {
        alert('请选择课程分类')
        return false
      }
      
      if (!courseData.value.difficultyLevel) {
        alert('请选择难度级别')
        return false
      }
      
      // 验证章节
      for (let i = 0; i < courseData.value.chapters.length; i++) {
        const chapter = courseData.value.chapters[i]
        if (!chapter.title.trim()) {
          alert(`第${i + 1}个章节的标题不能为空`)
          return false
        }
        if (!chapter.contentType) {
          alert(`第${i + 1}个章节的内容类型不能为空`)
          return false
        }
      }
      
      return true
    }
    
    const handleSubmit = async () => {
      if (!validateForm()) return
      
      submitting.value = true
      
      try {
        const submitData = {
          ...courseData.value,
          chapters: courseData.value.chapters.map((chapter, index) => ({
            ...chapter,
            orderIndex: index
          }))
        }
        
        if (isEdit.value) {
          await store.dispatch('course/updateCourse', {
            courseId: courseId.value,
            courseData: submitData
          })
        } else {
          await store.dispatch('course/createCourse', submitData)
        }
        
        store.dispatch('setError', null)
        router.push('/course/manage')
        
      } catch (error) {
        console.error('保存课程失败:', error)
        store.dispatch('setError', error.response?.data?.message || '保存课程失败，请稍后重试')
      } finally {
        submitting.value = false
      }
    }
    
    onMounted(() => {
      loadCourseData()
    })
    
    return {
      submitting,
      fileInput,
      isEdit,
      courseData,
      addChapter,
      removeChapter,
      moveChapter,
      handleImageUpload,
      removeCoverImage,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.breadcrumb {
  margin-bottom: 10px;
  color: #7f8c8d;
  font-size: 14px;
}

.breadcrumb a {
  color: #3498db;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.page-header h1 {
  color: #2c3e50;
  margin-bottom: 30px;
  font-size: 32px;
}

.course-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  overflow: hidden;
}

.form-section {
  padding: 30px;
  border-bottom: 1px solid #e1e8ed;
}

.form-section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.section-title {
  color: #2c3e50;
  margin: 0 0 25px 0;
  font-size: 20px;
  font-weight: 600;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  color: #2c3e50;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-help {
  margin-top: 5px;
  font-size: 12px;
  color: #7f8c8d;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.image-upload {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: border-color 0.3s;
}

.image-upload:hover {
  border-color: #3498db;
}

.image-preview {
  position: relative;
  display: inline-block;
}

.image-preview img {
  max-width: 300px;
  max-height: 200px;
  border-radius: 8px;
}

.remove-image {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e74c3c;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 12px;
}

.upload-placeholder {
  padding: 40px 20px;
}

.upload-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  margin-bottom: 10px;
}

.upload-btn:hover {
  background: #2980b9;
}

.upload-help {
  font-size: 12px;
  color: #7f8c8d;
}

.empty-chapters {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;
  background: #f8f9fa;
  border-radius: 8px;
}

.chapters-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chapter-item {
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  overflow: hidden;
}

.chapter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e8ed;
}

.chapter-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
}

.chapter-controls {
  display: flex;
  gap: 8px;
}

.chapter-content {
  padding: 20px;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.radio-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  cursor: pointer;
  padding: 15px;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  transition: all 0.3s;
}

.radio-item:hover {
  border-color: #3498db;
  background: #f8f9fa;
}

.radio-item input[type="radio"] {
  margin-top: 2px;
}

.radio-label {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 5px;
}

.radio-help {
  font-size: 12px;
  color: #7f8c8d;
}

.form-actions {
  padding: 30px;
  background: #f8f9fa;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

@media (max-width: 768px) {
  .form-section {
    padding: 20px;
  }
  
  .section-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .chapter-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .chapter-controls {
    justify-content: center;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>