<template>
  <div class="course-manage-page">
    <div class="container">
      <div class="page-header">
        <h1>我的课程</h1>
        <div class="header-actions">
          <router-link to="/course/create" class="btn btn-primary">
            ➕ 创建新课程
          </router-link>
        </div>
      </div>
      
      <!-- 课程统计 -->
      <div class="stats-section">
        <div class="stat-card">
          <div class="stat-number">{{ totalCourses }}</div>
          <div class="stat-label">总课程数</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ publishedCourses }}</div>
          <div class="stat-label">已发布</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ draftCourses }}</div>
          <div class="stat-label">草稿</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ totalStudents }}</div>
          <div class="stat-label">学生总数</div>
        </div>
      </div>
      
      <!-- 筛选和搜索 -->
      <div class="filters-section">
        <div class="search-box">
          <input
            v-model="searchQuery"
            type="text"
            class="form-control"
            placeholder="搜索我的课程..."
            @keyup.enter="handleSearch"
          >
          <button class="search-btn" @click="handleSearch">
            🔍
          </button>
        </div>
        
        <div class="filters">
          <select v-model="statusFilter" class="form-control" @change="handleFilter">
            <option value="">所有状态</option>
            <option value="DRAFT">草稿</option>
            <option value="PUBLISHED">已发布</option>
            <option value="ARCHIVED">已归档</option>
          </select>
          
          <select v-model="sortBy" class="form-control" @change="handleFilter">
            <option value="created_at">创建时间</option>
            <option value="updated_at">更新时间</option>
            <option value="students_count">学生数量</option>
          </select>
        </div>
      </div>
      
      <!-- 课程列表 -->
      <div class="courses-section">
        <div v-if="loading" class="loading">
          加载中...
        </div>
        
        <div v-else-if="myCourses.length === 0" class="empty-state">
          <div class="empty-icon">📚</div>
          <h3>还没有课程</h3>
          <p>开始创建您的第一门课程吧！</p>
          <router-link to="/course/create" class="btn btn-primary">
            创建课程
          </router-link>
        </div>
        
        <div v-else class="courses-list">
          <div 
            v-for="course in myCourses" 
            :key="course.id" 
            class="course-item"
          >
            <div class="course-image">
              <img :src="course.coverImage || '/placeholder-course.jpg'" :alt="course.title">
              <div class="course-status" :class="course.status.toLowerCase()">
                {{ getStatusText(course.status) }}
              </div>
            </div>
            
            <div class="course-content">
              <div class="course-header">
                <h3 class="course-title">{{ course.title }}</h3>
                <div class="course-actions">
                  <button 
                    class="btn btn-outline btn-sm"
                    @click="viewCourse(course.id)"
                  >
                    预览
                  </button>
                  <button 
                    class="btn btn-primary btn-sm"
                    @click="editCourse(course.id)"
                  >
                    编辑
                  </button>
                  <button 
                    class="btn btn-danger btn-sm"
                    @click="deleteCourse(course.id)"
                  >
                    删除
                  </button>
                </div>
              </div>
              
              <p class="course-description">{{ course.description }}</p>
              
              <div class="course-meta">
                <div class="meta-row">
                  <div class="meta-item">
                    <span class="meta-label">分类：</span>
                    <span class="meta-value">{{ course.categoryName || '未分类' }}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">难度：</span>
                    <span class="meta-value">{{ getLevelText(course.difficultyLevel) }}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">价格：</span>
                    <span class="meta-value">
                      {{ course.price === 0 ? '免费' : `¥${course.price}` }}
                    </span>
                  </div>
                </div>
                
                <div class="meta-row">
                  <div class="meta-item">
                    <span class="meta-label">学生：</span>
                    <span class="meta-value">{{ course.studentsCount || 0 }}人</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">章节：</span>
                    <span class="meta-value">{{ course.chaptersCount || 0 }}个</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">评分：</span>
                    <span class="meta-value">⭐ {{ course.rating || '暂无' }}</span>
                  </div>
                </div>
                
                <div class="meta-row">
                  <div class="meta-item">
                    <span class="meta-label">创建时间：</span>
                    <span class="meta-value">{{ formatDate(course.createdAt) }}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">更新时间：</span>
                    <span class="meta-value">{{ formatDate(course.updatedAt) }}</span>
                  </div>
                </div>
              </div>
              
              <div class="course-footer">
                <div class="status-actions">
                  <button 
                    v-if="course.status === 'DRAFT'"
                    class="btn btn-success btn-sm"
                    @click="publishCourse(course.id)"
                  >
                    发布课程
                  </button>
                  <button 
                    v-if="course.status === 'PUBLISHED'"
                    class="btn btn-warning btn-sm"
                    @click="archiveCourse(course.id)"
                  >
                    归档课程
                  </button>
                  <button 
                    v-if="course.status === 'ARCHIVED'"
                    class="btn btn-success btn-sm"
                    @click="publishCourse(course.id)"
                  >
                    重新发布
                  </button>
                </div>
                
                <div class="quick-actions">
                  <button 
                    class="btn btn-outline btn-sm"
                    @click="viewStudents(course.id)"
                  >
                    查看学生
                  </button>
                  <button 
                    class="btn btn-outline btn-sm"
                    @click="viewDiscussions(course.id)"
                  >
                    课程讨论
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination">
          <button 
            class="btn btn-outline"
            :disabled="currentPage === 1"
            @click="changePage(currentPage - 1)"
          >
            上一页
          </button>
          
          <span class="page-info">
            第 {{ currentPage }} 页，共 {{ totalPages }} 页
          </span>
          
          <button 
            class="btn btn-outline"
            :disabled="currentPage === totalPages"
            @click="changePage(currentPage + 1)"
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'CourseManage',
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const searchQuery = ref('')
    const statusFilter = ref('')
    const sortBy = ref('created_at')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const loading = ref(false)
    const totalPages = ref(1)
    
    const myCourses = ref([])
    
    const totalCourses = computed(() => myCourses.value.length)
    const publishedCourses = computed(() => 
      myCourses.value.filter(course => course.status === 'PUBLISHED').length
    )
    const draftCourses = computed(() => 
      myCourses.value.filter(course => course.status === 'DRAFT').length
    )
    const totalStudents = computed(() => 
      myCourses.value.reduce((total, course) => total + (course.studentsCount || 0), 0)
    )
    
    const getStatusText = (status) => {
      const statusMap = {
        'DRAFT': '草稿',
        'PUBLISHED': '已发布',
        'ARCHIVED': '已归档'
      }
      return statusMap[status] || status
    }
    
    const getLevelText = (level) => {
      const levelMap = {
        'BEGINNER': '初级',
        'INTERMEDIATE': '中级',
        'ADVANCED': '高级'
      }
      return levelMap[level] || level
    }
    
    const formatDate = (timestamp) => {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return date.toLocaleDateString()
    }
    
    const fetchMyCourses = async () => {
      loading.value = true
      try {
        const params = {
          page: currentPage.value,
          size: pageSize.value,
          sort: sortBy.value,
          teacherOnly: true // 只获取当前用户创建的课程
        }
        
        if (statusFilter.value) {
          params.status = statusFilter.value
        }
        
        if (searchQuery.value.trim()) {
          params.keyword = searchQuery.value.trim()
        }
        
        const response = await store.dispatch('course/fetchCourses', params)
        myCourses.value = response.data.content || response.data || []
        
        if (response.data && response.data.totalPages) {
          totalPages.value = response.data.totalPages
        }
        
      } catch (error) {
        console.error('获取课程列表失败:', error)
        store.dispatch('setError', '获取课程列表失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
    
    const handleSearch = () => {
      currentPage.value = 1
      fetchMyCourses()
    }
    
    const handleFilter = () => {
      currentPage.value = 1
      fetchMyCourses()
    }
    
    const changePage = (page) => {
      currentPage.value = page
      fetchMyCourses()
    }
    
    const viewCourse = (courseId) => {
      router.push(`/course/${courseId}`)
    }
    
    const editCourse = (courseId) => {
      router.push(`/course/edit/${courseId}`)
    }
    
    const deleteCourse = async (courseId) => {
      if (!confirm('确定要删除这门课程吗？此操作不可恢复。')) {
        return
      }
      
      try {
        await store.dispatch('course/deleteCourse', courseId)
        store.dispatch('setError', null)
        // 重新获取课程列表
        fetchMyCourses()
      } catch (error) {
        console.error('删除课程失败:', error)
        store.dispatch('setError', error.response?.data?.message || '删除课程失败，请稍后重试')
      }
    }
    
    const publishCourse = async (courseId) => {
      try {
        await store.dispatch('course/updateCourse', {
          courseId,
          courseData: { status: 'PUBLISHED' }
        })
        store.dispatch('setError', null)
        fetchMyCourses()
      } catch (error) {
        console.error('发布课程失败:', error)
        store.dispatch('setError', error.response?.data?.message || '发布课程失败，请稍后重试')
      }
    }
    
    const archiveCourse = async (courseId) => {
      try {
        await store.dispatch('course/updateCourse', {
          courseId,
          courseData: { status: 'ARCHIVED' }
        })
        store.dispatch('setError', null)
        fetchMyCourses()
      } catch (error) {
        console.error('归档课程失败:', error)
        store.dispatch('setError', error.response?.data?.message || '归档课程失败，请稍后重试')
      }
    }
    
    const viewStudents = (courseId) => {
      router.push(`/course/${courseId}/students`)
    }
    
    const viewDiscussions = (courseId) => {
      router.push(`/discussion/${courseId}`)
    }
    
    onMounted(() => {
      fetchMyCourses()
    })
    
    return {
      searchQuery,
      statusFilter,
      sortBy,
      currentPage,
      totalPages,
      loading,
      myCourses,
      totalCourses,
      publishedCourses,
      draftCourses,
      totalStudents,
      getStatusText,
      getLevelText,
      formatDate,
      handleSearch,
      handleFilter,
      changePage,
      viewCourse,
      editCourse,
      deleteCourse,
      publishCourse,
      archiveCourse,
      viewStudents,
      viewDiscussions
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: #2c3e50;
  margin: 0;
  font-size: 32px;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-number {
  font-size: 36px;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 8px;
}

.stat-label {
  color: #7f8c8d;
  font-size: 14px;
}

.filters-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.search-box {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.search-box .form-control {
  flex: 1;
}

.search-btn {
  padding: 10px 20px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
}

.search-btn:hover {
  background-color: #2980b9;
}

.filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.courses-section {
  min-height: 400px;
}

.loading {
  text-align: center;
  padding: 60px 0;
  font-size: 18px;
  color: #7f8c8d;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.empty-state p {
  color: #7f8c8d;
  margin-bottom: 25px;
}

.courses-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;
}

.course-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 0;
}

.course-image {
  position: relative;
  height: 200px;
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.course-status.draft {
  background-color: #f39c12;
}

.course-status.published {
  background-color: #27ae60;
}

.course-status.archived {
  background-color: #95a5a6;
}

.course-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.course-title {
  color: #2c3e50;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  flex: 1;
  margin-right: 15px;
}

.course-actions {
  display: flex;
  gap: 8px;
}

.course-description {
  color: #7f8c8d;
  margin-bottom: 20px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-meta {
  margin-bottom: 20px;
}

.meta-row {
  display: flex;
  gap: 30px;
  margin-bottom: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.meta-label {
  color: #7f8c8d;
  margin-right: 5px;
  font-weight: 500;
}

.meta-value {
  color: #2c3e50;
  font-weight: 600;
}

.course-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #ecf0f1;
  margin-top: auto;
}

.status-actions,
.quick-actions {
  display: flex;
  gap: 8px;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 40px;
}

.page-info {
  color: #7f8c8d;
  font-size: 14px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .stats-section {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .filters-section {
    padding: 20px;
  }
  
  .filters {
    grid-template-columns: 1fr;
  }
  
  .course-item {
    grid-template-columns: 1fr;
  }
  
  .course-image {
    height: 150px;
  }
  
  .course-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .course-actions {
    justify-content: flex-start;
  }
  
  .meta-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .course-footer {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .status-actions,
  .quick-actions {
    justify-content: center;
  }
}
</style>