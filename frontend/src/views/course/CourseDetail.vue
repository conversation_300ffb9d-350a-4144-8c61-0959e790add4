<template>
  <div class="course-detail-page">
    <div class="container">
      <div v-if="loading" class="loading">
        加载中...
      </div>
      
      <div v-else-if="course" class="course-detail">
        <!-- 课程头部信息 -->
        <div class="course-header">
          <div class="course-info">
            <div class="breadcrumb">
              <router-link to="/courses">课程中心</router-link>
              <span> / </span>
              <span>{{ course.title }}</span>
            </div>
            
            <h1 class="course-title">{{ course.title }}</h1>
            <p class="course-description">{{ course.description }}</p>
            
            <div class="course-meta">
              <div class="meta-item">
                <span class="meta-label">讲师：</span>
                <span class="meta-value">{{ course.teacherName || '未知讲师' }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">难度：</span>
                <span class="meta-value">{{ getLevelText(course.difficultyLevel) }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">学习人数：</span>
                <span class="meta-value">{{ course.studentsCount || 0 }}人</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">评分：</span>
                <span class="meta-value">⭐ {{ course.rating || '暂无评分' }}</span>
              </div>
            </div>
            
            <div class="course-actions">
              <div class="price-info">
                <span v-if="course.price === 0" class="free-price">免费</span>
                <span v-else class="price">¥{{ course.price }}</span>
              </div>
              
              <button
                v-if="!isEnrolled"
                class="btn btn-primary btn-large"
                @click="handleEnroll"
                :disabled="enrolling"
              >
                <span v-if="enrolling">报名中...</span>
                <span v-else>立即报名</span>
              </button>

              <div v-else class="enrolled-actions">
                <button
                  class="btn btn-success btn-large"
                  @click="goToLearning"
                >
                  ✅ 已报名 - 开始学习
                </button>
                <button
                  class="btn btn-outline btn-small"
                  @click="handleUnenroll"
                  :disabled="enrolling"
                >
                  <span v-if="enrolling">处理中...</span>
                  <span v-else>退选课程</span>
                </button>
              </div>
            </div>
          </div>
          
          <div class="course-image">
            <img :src="course.coverImage || '/placeholder-course.jpg'" :alt="course.title">
          </div>
        </div>
        
        <!-- 课程内容 -->
        <div class="course-content">
          <div class="content-tabs">
            <button 
              class="tab-btn"
              :class="{ active: activeTab === 'chapters' }"
              @click="activeTab = 'chapters'"
            >
              课程章节
            </button>
            <button 
              class="tab-btn"
              :class="{ active: activeTab === 'discussion' }"
              @click="activeTab = 'discussion'"
            >
              课程讨论
            </button>
            <button 
              class="tab-btn"
              :class="{ active: activeTab === 'related' }"
              @click="activeTab = 'related'"
            >
              相关推荐
            </button>
          </div>
          
          <!-- 课程章节 -->
          <div v-if="activeTab === 'chapters'" class="tab-content">
            <div class="chapters-list">
              <div v-if="chapters.length === 0" class="empty-chapters">
                <p>暂无课程章节</p>
              </div>
              <div v-else>
                <div 
                  v-for="(chapter, index) in chapters" 
                  :key="chapter.id"
                  class="chapter-item"
                >
                  <div class="chapter-header">
                    <div class="chapter-number">{{ index + 1 }}</div>
                    <div class="chapter-info">
                      <h3 class="chapter-title">{{ chapter.title }}</h3>
                      <p class="chapter-description">{{ chapter.description }}</p>
                      <div class="chapter-meta">
                        <span class="chapter-type">{{ getContentTypeText(chapter.contentType) }}</span>
                        <span class="chapter-duration">
                          {{ chapter.duration || '15分钟' }}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="chapter-actions">
                    <button
                      class="btn btn-outline btn-sm"
                      @click="previewChapter(chapter)"
                    >
                      {{ chapter.contentType === 'VIDEO' ? '🎥 预览' : chapter.contentType === 'QUIZ' ? '❓ 预览' : '📄 预览' }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 课程讨论 -->
          <div v-if="activeTab === 'discussion'" class="tab-content">
            <div class="discussion-section">
              <div class="discussion-header">
                <h3>课程讨论</h3>
                <button class="btn btn-primary" @click="showCreateDiscussion = true">
                  发起讨论
                </button>
              </div>
              
              <div class="discussions-list">
                <div v-if="discussions.length === 0" class="empty-discussions">
                  <p>暂无讨论，快来发起第一个讨论吧！</p>
                </div>
                <div v-else>
                  <div 
                    v-for="discussion in discussions" 
                    :key="discussion.id"
                    class="discussion-item"
                    @click="goToDiscussion(discussion.id)"
                  >
                    <div class="discussion-content">
                      <h4 class="discussion-title">{{ discussion.title }}</h4>
                      <p class="discussion-preview">{{ discussion.content }}</p>
                      <div class="discussion-meta">
                        <span class="author">{{ discussion.nickname || discussion.username }}</span>
                        <span class="time">{{ formatTime(discussion.createdAt) }}</span>
                        <span class="replies">{{ discussion.replyCount || 0 }}回复</span>
                        <span class="likes">{{ discussion.likeCount || 0 }}点赞</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 相关推荐 -->
          <div v-if="activeTab === 'related'" class="tab-content">
            <div class="related-courses">
              <h3>相关课程推荐</h3>
              <div v-if="relatedCourses.length === 0" class="empty-related">
                <p>正在为您生成个性化推荐...</p>
                <div class="loading-spinner">🔄</div>
              </div>
              <div v-else class="related-courses-grid">
                <div
                  v-for="relatedCourse in relatedCourses"
                  :key="relatedCourse.courseId || relatedCourse.id"
                  class="related-course-card"
                  @click="goToCourse(relatedCourse.courseId || relatedCourse.id)"
                >
                  <img :src="relatedCourse.coverImage || '/placeholder-course.jpg'" :alt="relatedCourse.title">
                  <div class="related-course-info">
                    <h4>{{ relatedCourse.title }}</h4>
                    <p>{{ relatedCourse.description }}</p>
                    <div class="related-course-meta">
                      <span class="price">
                        {{ relatedCourse.price === 0 ? '免费' : `¥${relatedCourse.price}` }}
                      </span>
                      <span class="students">{{ relatedCourse.studentsCount || 0 }}人学习</span>
                      <span v-if="relatedCourse.score" class="recommendation-score">
                        推荐度: {{ Math.round(relatedCourse.score * 100) }}%
                      </span>
                    </div>
                    <div v-if="relatedCourse.reason" class="recommendation-reason">
                      {{ relatedCourse.reason }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else class="error-state">
        <h3>课程不存在</h3>
        <p>您访问的课程可能已被删除或不存在</p>
        <router-link to="/courses" class="btn btn-primary">
          返回课程列表
        </router-link>
      </div>
    </div>

    <!-- 支付弹窗 -->
    <PaymentModal
      :visible="showPaymentModal"
      :course="course"
      @close="closePaymentModal"
      @success="handlePaymentSuccess"
      @failure="handlePaymentFailure"
    />

    <!-- 创建讨论弹窗 -->
    <div v-if="showCreateDiscussion" class="modal-overlay" @click="closeCreateDiscussion">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>发起讨论</h3>
          <button class="close-btn" @click="closeCreateDiscussion">&times;</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitDiscussion">
            <div class="form-group">
              <label for="discussionTitle">讨论标题</label>
              <input
                id="discussionTitle"
                v-model="discussionForm.title"
                type="text"
                class="form-control"
                placeholder="请输入讨论标题"
                required
              />
            </div>
            <div class="form-group">
              <label for="discussionContent">讨论内容</label>
              <textarea
                id="discussionContent"
                v-model="discussionForm.content"
                class="form-control"
                rows="6"
                placeholder="请详细描述您的问题或想法..."
                required
              ></textarea>
            </div>
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" @click="closeCreateDiscussion">
                取消
              </button>
              <button type="submit" class="btn btn-primary" :disabled="submittingDiscussion">
                {{ submittingDiscussion ? '发布中...' : '发布讨论' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import courseApi from '@/api/course'
import learningApi from '@/api/learning'
import recommendationApi from '@/api/recommendation'
import PaymentModal from '@/components/PaymentModal.vue'

export default {
  name: 'CourseDetail',
  components: {
    PaymentModal
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()
    
    const loading = ref(true)
    const enrolling = ref(false)
    const activeTab = ref('chapters')
    const showCreateDiscussion = ref(false)
    const isEnrolled = ref(false)
    const showPaymentModal = ref(false)
    const submittingDiscussion = ref(false)
    const chapters = ref([])
    const discussions = ref([])
    const relatedCourses = ref([])

    // 讨论表单数据
    const discussionForm = ref({
      title: '',
      content: ''
    })
    
    const course = computed(() => store.getters['course/currentCourse'])
    
    const courseId = computed(() => route.params.id)
    
    const getLevelText = (level) => {
      const levelMap = {
        'BEGINNER': '初级',
        'INTERMEDIATE': '中级',
        'ADVANCED': '高级'
      }
      return levelMap[level] || level
    }
    
    const getContentTypeText = (type) => {
      const typeMap = {
        'VIDEO': '视频',
        'DOCUMENT': '文档',
        'QUIZ': '测验'
      }
      return typeMap[type] || type
    }
    
    const formatTime = (timestamp) => {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
    }

    const fetchChapters = async (courseId) => {
      try {
        const response = await courseApi.getCourseChapters(courseId)
        if (response.data.success) {
          chapters.value = response.data.data || []
        } else {
          console.error('获取课程章节失败:', response.data.message)
          // 如果API不存在，使用模拟数据
          chapters.value = [
            {
              id: 1,
              title: '课程介绍',
              description: '了解课程目标和学习路径',
              contentType: 'VIDEO',
              duration: '10分钟',
              orderIndex: 1
            },
            {
              id: 2,
              title: '基础概念',
              description: '掌握核心概念和基础知识',
              contentType: 'DOCUMENT',
              duration: '20分钟',
              orderIndex: 2
            },
            {
              id: 3,
              title: '实践练习',
              description: '通过实际操作加深理解',
              contentType: 'VIDEO',
              duration: '30分钟',
              orderIndex: 3
            },
            {
              id: 4,
              title: '章节测验',
              description: '检验学习成果',
              contentType: 'QUIZ',
              duration: '15分钟',
              orderIndex: 4
            }
          ]
        }
      } catch (error) {
        console.error('获取课程章节失败:', error)
        // 使用模拟数据
        chapters.value = [
          {
            id: 1,
            title: '课程介绍',
            description: '了解课程目标和学习路径',
            contentType: 'VIDEO',
            duration: '10分钟',
            orderIndex: 1
          },
          {
            id: 2,
            title: '基础概念',
            description: '掌握核心概念和基础知识',
            contentType: 'DOCUMENT',
            duration: '20分钟',
            orderIndex: 2
          }
        ]
      }
    }

    const checkEnrollmentStatus = async () => {
      try {
        const response = await learningApi.checkEnrollment(courseId.value)
        if (response.data.success) {
          isEnrolled.value = response.data.data
        }
      } catch (error) {
        console.error('检查报名状态失败:', error)
        isEnrolled.value = false
      }
    }

    const fetchDiscussions = async (courseId) => {
      try {
        // 调用真实的讨论API
        const response = await store.dispatch('discussion/fetchDiscussions', {
          courseId,
          page: 1,
          size: 5
        })

        if (response.data.success) {
          discussions.value = response.data.data?.records || []
        } else {
          console.error('获取讨论失败:', response.data.message)
          discussions.value = []
        }
      } catch (error) {
        console.error('获取课程讨论失败:', error)
        discussions.value = []
      }
    }

    const fetchRelatedCourses = async (courseId) => {
      try {
        // 优先使用协同过滤推荐
        const isAuthenticated = store.getters['auth/isAuthenticated']
        if (isAuthenticated) {
          const user = store.getters['auth/user']
          if (user && user.id) {
            // 使用协同过滤推荐
            const response = await recommendationApi.getCollaborativeRecommendations(user.id, 6)
            if (response.data.success && response.data.data.length > 0) {
              // 获取推荐课程的详细信息
              const recommendedCourses = await Promise.all(
                response.data.data.map(async (rec) => {
                  try {
                    const courseResponse = await courseApi.getCourse(rec.courseId)
                    if (courseResponse.data.success) {
                      return {
                        ...courseResponse.data.data,
                        score: rec.score,
                        reason: rec.reason
                      }
                    }
                  } catch (error) {
                    console.error(`获取课程${rec.courseId}详情失败:`, error)
                    return null
                  }
                }
                )
              )
              relatedCourses.value = recommendedCourses.filter(course => course !== null)
              return
            }
          }
        }

        // 如果协同过滤失败，使用基于课程的相关推荐
        const response = await recommendationApi.getRelatedCourses(courseId, 6)
        if (response.data.success) {
          relatedCourses.value = response.data.data || []
        }
      } catch (error) {
        console.error('获取相关课程推荐失败:', error)
        relatedCourses.value = []
      }
    }

    const fetchCourseDetail = async () => {
      loading.value = true
      try {
        await store.dispatch('course/fetchCourse', courseId.value)

        // 获取课程章节
        await fetchChapters(courseId.value)

        // 检查用户是否已报名（需要登录）
        const isAuthenticated = store.getters['auth/isAuthenticated']
        if (isAuthenticated) {
          await checkEnrollmentStatus()
        }

        // 获取课程讨论
        await fetchDiscussions(courseId.value)

        // 获取相关课程推荐
        await fetchRelatedCourses(courseId.value)

      } catch (error) {
        console.error('获取课程详情失败:', error)
        store.dispatch('setError', '获取课程详情失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
    
    const handleEnroll = async () => {
      const isAuthenticated = store.getters['auth/isAuthenticated']
      if (!isAuthenticated) {
        router.push('/login')
        return
      }

      // 检查是否为付费课程
      if (course.value && course.value.price > 0) {
        // 付费课程，显示支付弹窗
        showPaymentModal.value = true
        return
      }

      // 免费课程，直接报名
      enrolling.value = true
      try {
        await store.dispatch('course/enrollCourse', courseId.value)
        // 报名成功，更新状态
        isEnrolled.value = true
        // 跳转到学习页面
        router.push(`/learning/course/${courseId.value}`)
      } catch (error) {
        console.error('报名失败:', error)
        store.dispatch('setError', error.response?.data?.message || '报名失败，请稍后重试')
      } finally {
        enrolling.value = false
      }
    }

    const goToLearning = () => {
      router.push(`/learning/course/${courseId.value}`)
    }

    const previewChapter = (chapter) => {
      if (chapter.contentType === 'VIDEO') {
        alert(`预览视频章节: ${chapter.title}\n\n${chapter.description}\n\n这里将播放课程预览视频...`)
      } else if (chapter.contentType === 'DOCUMENT') {
        alert(`预览文档章节: ${chapter.title}\n\n${chapter.description}\n\n这里将显示文档预览内容...`)
      } else if (chapter.contentType === 'QUIZ') {
        alert(`预览测验章节: ${chapter.title}\n\n${chapter.description}\n\n这里将显示测验题目预览...`)
      }
    }
    
    const goToCourse = (id) => {
      router.push(`/course/${id}`)
    }
    
    const goToDiscussion = (discussionId) => {
      router.push(`/discussion/${courseId.value}?discussionId=${discussionId}`)
    }

    // 支付成功处理
    const handlePaymentSuccess = async (paymentResult) => {
      showPaymentModal.value = false

      try {
        // 支付成功后自动报名
        await store.dispatch('course/enrollCourse', courseId.value)
        isEnrolled.value = true

        // 显示成功消息
        store.dispatch('setSuccess', '支付成功！您已成功报名课程，现在可以开始学习了。')

        // 跳转到学习页面
        setTimeout(() => {
          router.push(`/learning/course/${courseId.value}`)
        }, 2000)

      } catch (error) {
        console.error('支付后报名失败:', error)
        store.dispatch('setError', '支付成功但报名失败，请联系客服处理')
      }
    }

    // 支付失败处理
    const handlePaymentFailure = (paymentResult) => {
      showPaymentModal.value = false
      store.dispatch('setError', paymentResult.message || '支付失败，请重试')
    }

    // 关闭支付弹窗
    const closePaymentModal = () => {
      showPaymentModal.value = false
    }

    // 关闭创建讨论弹窗
    const closeCreateDiscussion = () => {
      showCreateDiscussion.value = false
      discussionForm.value = {
        title: '',
        content: ''
      }
    }

    // 提交讨论
    const submitDiscussion = async () => {
      if (!discussionForm.value.title.trim() || !discussionForm.value.content.trim()) {
        store.dispatch('setError', '请填写完整的讨论标题和内容')
        return
      }

      submittingDiscussion.value = true
      try {
        const discussionData = {
          courseId: courseId.value,
          title: discussionForm.value.title.trim(),
          content: discussionForm.value.content.trim()
        }

        await store.dispatch('discussion/createDiscussion', discussionData)

        // 创建成功，关闭弹窗并刷新讨论列表
        closeCreateDiscussion()
        await fetchDiscussions(courseId.value)

        store.dispatch('setSuccess', '讨论发布成功！')
      } catch (error) {
        console.error('创建讨论失败:', error)
        store.dispatch('setError', error.response?.data?.message || '发布讨论失败，请重试')
      } finally {
        submittingDiscussion.value = false
      }
    }

    // 退选课程
    const handleUnenroll = async () => {
      if (!confirm('确定要退选这门课程吗？退选后您将无法继续学习课程内容。')) {
        return
      }

      enrolling.value = true
      try {
        // 调用退选API
        await learningApi.unenrollCourse(courseId.value)
        isEnrolled.value = false
        store.dispatch('setSuccess', '已成功退选课程')
      } catch (error) {
        console.error('退选失败:', error)
        store.dispatch('setError', error.response?.data?.message || '退选失败，请稍后重试')
      } finally {
        enrolling.value = false
      }
    }
    
    onMounted(() => {
      fetchCourseDetail()
    })
    
    return {
      loading,
      enrolling,
      activeTab,
      showCreateDiscussion,
      isEnrolled,
      showPaymentModal,
      submittingDiscussion,
      discussionForm,
      course,
      chapters,
      discussions,
      relatedCourses,
      getLevelText,
      getContentTypeText,
      formatTime,
      handleEnroll,
      handleUnenroll,
      goToLearning,
      goToCourse,
      goToDiscussion,
      previewChapter,
      handlePaymentSuccess,
      handlePaymentFailure,
      closePaymentModal,
      closeCreateDiscussion,
      submitDiscussion
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.loading {
  text-align: center;
  padding: 60px 0;
  font-size: 18px;
  color: #7f8c8d;
}

.breadcrumb {
  margin-bottom: 20px;
  color: #7f8c8d;
  font-size: 14px;
}

.breadcrumb a {
  color: #3498db;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.course-header {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.course-title {
  color: #2c3e50;
  font-size: 32px;
  margin-bottom: 15px;
  line-height: 1.3;
}

.course-description {
  color: #7f8c8d;
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 25px;
}

.course-meta {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 30px;
}

.meta-item {
  display: flex;
  align-items: center;
}

.meta-label {
  color: #7f8c8d;
  margin-right: 8px;
  font-weight: 500;
}

.meta-value {
  color: #2c3e50;
  font-weight: 600;
}

.course-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.free-price {
  color: #27ae60;
  font-size: 24px;
  font-weight: bold;
}

.price {
  color: #e74c3c;
  font-size: 24px;
  font-weight: bold;
}

.btn-large {
  padding: 15px 30px;
  font-size: 18px;
}

.btn-small {
  padding: 8px 16px;
  font-size: 14px;
}

.btn-outline {
  background-color: transparent;
  border: 2px solid #e74c3c;
  color: #e74c3c;
}

.btn-outline:hover:not(:disabled) {
  background-color: #e74c3c;
  color: white;
}

.enrolled-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.enrolled-actions .btn-large {
  margin-bottom: 0;
}

.course-image {
  display: flex;
  align-items: center;
}

.course-image img {
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.course-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  overflow: hidden;
}

.content-tabs {
  display: flex;
  border-bottom: 1px solid #e1e8ed;
}

.tab-btn {
  flex: 1;
  padding: 20px;
  border: none;
  background: none;
  font-size: 16px;
  font-weight: 500;
  color: #7f8c8d;
  cursor: pointer;
  transition: all 0.3s;
}

.tab-btn.active {
  color: #3498db;
  border-bottom: 2px solid #3498db;
}

.tab-btn:hover {
  background-color: #f8f9fa;
}

.tab-content {
  padding: 30px;
}

.chapters-list {
  max-height: 600px;
  overflow-y: auto;
}

.chapter-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  margin-bottom: 15px;
  transition: all 0.3s;
}

.chapter-item:hover {
  border-color: #3498db;
  background-color: #f8f9fa;
}

.chapter-header {
  display: flex;
  align-items: center;
  flex: 1;
}

.chapter-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 15px;
}

.chapter-title {
  color: #2c3e50;
  margin-bottom: 5px;
  font-size: 16px;
}

.chapter-meta {
  display: flex;
  gap: 15px;
  font-size: 14px;
  color: #7f8c8d;
}

.chapter-type {
  background-color: #ecf0f1;
  padding: 2px 8px;
  border-radius: 4px;
}

.discussion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.discussion-header h3 {
  color: #2c3e50;
  margin: 0;
}

.discussion-item {
  padding: 20px;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s;
}

.discussion-item:hover {
  border-color: #3498db;
  background-color: #f8f9fa;
}

.discussion-title {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 16px;
}

.discussion-preview {
  color: #7f8c8d;
  margin-bottom: 15px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.discussion-meta {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #7f8c8d;
}

.related-courses h3 {
  color: #2c3e50;
  margin-bottom: 25px;
}

.related-courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.related-course-card {
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
}

.related-course-card:hover {
  border-color: #3498db;
  transform: translateY(-2px);
}

.related-course-card img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.related-course-info {
  padding: 15px;
}

.related-course-info h4 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 16px;
}

.related-course-info p {
  color: #7f8c8d;
  font-size: 14px;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-course-meta {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.related-course-meta .price {
  color: #e74c3c;
  font-weight: 600;
}

.related-course-meta .students {
  color: #7f8c8d;
}

.empty-chapters,
.empty-discussions,
.empty-related {
  text-align: center;
  padding: 40px 0;
  color: #7f8c8d;
}

.chapter-description {
  color: #7f8c8d;
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.4;
}

.btn-success {
  background-color: #27ae60;
  color: white;
}

.btn-success:hover {
  background-color: #229954;
}

.recommendation-score {
  background-color: #3498db;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.recommendation-reason {
  font-size: 12px;
  color: #7f8c8d;
  font-style: italic;
  margin-top: 5px;
}

.loading-spinner {
  font-size: 24px;
  animation: spin 2s linear infinite;
  margin-top: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state {
  text-align: center;
  padding: 60px 0;
}

.error-state h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.error-state p {
  color: #7f8c8d;
  margin-bottom: 25px;
}

@media (max-width: 768px) {
  .course-header {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .course-meta {
    grid-template-columns: 1fr;
  }
  
  .course-actions {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .content-tabs {
    flex-direction: column;
  }
  
  .chapter-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .discussion-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .related-courses-grid {
    grid-template-columns: 1fr;
  }
}

/* 创建讨论弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

textarea.form-control {
  resize: vertical;
  min-height: 120px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>