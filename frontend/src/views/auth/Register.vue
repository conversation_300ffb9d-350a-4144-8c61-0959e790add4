<template>
  <div class="register-page">
    <div class="container">
      <div class="register-card">
        <div class="register-header">
          <h2>用户注册</h2>
          <p>加入我们的学习社区</p>
        </div>
        
        <form @submit.prevent="handleRegister" class="register-form">
          <div class="form-group">
            <label for="username">用户名</label>
            <input
              id="username"
              v-model="form.username"
              type="text"
              class="form-control"
              :class="{ 'error': errors.username }"
              placeholder="请输入用户名"
              required
            >
            <span v-if="errors.username" class="error-message">{{ errors.username }}</span>
          </div>
          
          <div class="form-group">
            <label for="email">邮箱</label>
            <input
              id="email"
              v-model="form.email"
              type="email"
              class="form-control"
              :class="{ 'error': errors.email }"
              placeholder="请输入邮箱地址"
              required
            >
            <span v-if="errors.email" class="error-message">{{ errors.email }}</span>
          </div>
          
          <div class="form-group">
            <label for="password">密码</label>
            <input
              id="password"
              v-model="form.password"
              type="password"
              class="form-control"
              :class="{ 'error': errors.password }"
              placeholder="请输入密码（至少6位）"
              required
            >
            <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
          </div>
          
          <div class="form-group">
            <label for="confirmPassword">确认密码</label>
            <input
              id="confirmPassword"
              v-model="form.confirmPassword"
              type="password"
              class="form-control"
              :class="{ 'error': errors.confirmPassword }"
              placeholder="请再次输入密码"
              required
            >
            <span v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</span>
          </div>
          
          <div class="form-group">
            <label for="role">用户角色</label>
            <select
              id="role"
              v-model="form.role"
              class="form-control"
              :class="{ 'error': errors.role }"
            >
              <option value="STUDENT">学生</option>
              <option value="TEACHER">教师</option>
            </select>
            <span v-if="errors.role" class="error-message">{{ errors.role }}</span>
          </div>
          
          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" v-model="form.agreeTerms" required>
              <span class="checkmark"></span>
              我已阅读并同意 <a href="#terms" class="terms-link">服务条款</a> 和 <a href="#privacy" class="terms-link">隐私政策</a>
            </label>
            <span v-if="errors.agreeTerms" class="error-message">{{ errors.agreeTerms }}</span>
          </div>
          
          <button 
            type="submit" 
            class="btn btn-primary btn-full"
            :disabled="loading"
          >
            <span v-if="loading">注册中...</span>
            <span v-else>注册</span>
          </button>
          
          <div class="register-footer">
            <p>已有账户？ <router-link to="/login">立即登录</router-link></p>
          </div>
        </form>
        
        <div v-if="error" class="error-alert">
          {{ error }}
        </div>
        
        <div v-if="success" class="success-alert">
          {{ success }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, ref, computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'Register',
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const form = reactive({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      role: 'STUDENT',
      agreeTerms: false
    })
    
    const errors = reactive({})
    const loading = ref(false)
    const success = ref('')
    const error = computed(() => store.state.error)
    
    const validateForm = () => {
      const newErrors = {}
      
      if (!form.username.trim()) {
        newErrors.username = '请输入用户名'
      } else if (form.username.length < 3) {
        newErrors.username = '用户名长度至少3位'
      }
      
      if (!form.email.trim()) {
        newErrors.email = '请输入邮箱'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
        newErrors.email = '请输入有效的邮箱地址'
      }
      
      if (!form.password.trim()) {
        newErrors.password = '请输入密码'
      } else if (form.password.length < 6) {
        newErrors.password = '密码长度至少6位'
      }
      
      if (!form.confirmPassword.trim()) {
        newErrors.confirmPassword = '请确认密码'
      } else if (form.password !== form.confirmPassword) {
        newErrors.confirmPassword = '两次输入的密码不一致'
      }
      
      if (!form.agreeTerms) {
        newErrors.agreeTerms = '请同意服务条款和隐私政策'
      }
      
      Object.assign(errors, newErrors)
      return Object.keys(newErrors).length === 0
    }
    
    const handleRegister = async () => {
      // 清除之前的错误和成功消息
      Object.keys(errors).forEach(key => delete errors[key])
      store.dispatch('clearError')
      success.value = ''
      
      if (!validateForm()) {
        return
      }
      
      loading.value = true
      
      try {
        await store.dispatch('auth/register', {
          username: form.username,
          email: form.email,
          password: form.password,
          confirmPassword: form.confirmPassword,
          role: form.role
        })
        
        success.value = '注册成功！请查收邮箱验证邮件，然后登录。'
        
        // 3秒后跳转到登录页面
        setTimeout(() => {
          router.push('/login')
        }, 3000)
        
      } catch (error) {
        console.error('注册失败:', error)
        store.dispatch('setError', error.response?.data?.message || '注册失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
    
    return {
      form,
      errors,
      loading,
      success,
      error,
      handleRegister
    }
  }
}
</script>

<style scoped>
.register-page {
  min-height: calc(100vh - 140px);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 40px 20px;
}

.container {
  width: 100%;
  max-width: 450px;
}

.register-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.1);
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-header h2 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 28px;
}

.register-header p {
  color: #7f8c8d;
  margin: 0;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #2c3e50;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
}

.form-control.error {
  border-color: #e74c3c;
}

.error-message {
  color: #e74c3c;
  font-size: 14px;
  margin-top: 5px;
  display: block;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 8px;
  margin-top: 2px;
}

.terms-link {
  color: #3498db;
  text-decoration: none;
}

.terms-link:hover {
  text-decoration: underline;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2980b9;
}

.btn-primary:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.btn-full {
  width: 100%;
}

.register-footer {
  text-align: center;
  margin-top: 25px;
  padding-top: 25px;
  border-top: 1px solid #e1e8ed;
}

.register-footer p {
  color: #7f8c8d;
  margin: 0;
}

.register-footer a {
  color: #3498db;
  text-decoration: none;
}

.register-footer a:hover {
  text-decoration: underline;
}

.error-alert {
  background-color: #fdf2f2;
  color: #e74c3c;
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 20px;
  border: 1px solid #fecaca;
}

.success-alert {
  background-color: #f0f9ff;
  color: #059669;
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 20px;
  border: 1px solid #a7f3d0;
}

@media (max-width: 480px) {
  .register-card {
    padding: 30px 20px;
  }
  
  .register-header h2 {
    font-size: 24px;
  }
}
</style>