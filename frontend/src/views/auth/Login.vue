<template>
  <div class="login-page">
    <div class="container">
      <div class="login-card">
        <div class="login-header">
          <h2>用户登录</h2>
          <p>欢迎回到学习平台</p>
        </div>
        
        <form @submit.prevent="handleLogin" class="login-form">
          <div class="form-group">
            <label for="username">用户名或邮箱</label>
            <input
              id="username"
              v-model="form.username"
              type="text"
              class="form-control"
              :class="{ 'error': errors.username }"
              placeholder="请输入用户名或邮箱"
              required
            >
            <span v-if="errors.username" class="error-message">{{ errors.username }}</span>
          </div>
          
          <div class="form-group">
            <label for="password">密码</label>
            <input
              id="password"
              v-model="form.password"
              type="password"
              class="form-control"
              :class="{ 'error': errors.password }"
              placeholder="请输入密码"
              required
            >
            <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
          </div>
          
          <div class="form-options">
            <label class="checkbox-label">
              <input type="checkbox" v-model="form.rememberMe">
              <span class="checkmark"></span>
              记住我
            </label>
            <router-link to="/forgot-password" class="forgot-link">
              忘记密码？
            </router-link>
          </div>
          
          <button 
            type="submit" 
            class="btn btn-primary btn-full"
            :disabled="loading"
          >
            <span v-if="loading">登录中...</span>
            <span v-else>登录</span>
          </button>
          
          <div class="login-footer">
            <p>还没有账户？ <router-link to="/register">立即注册</router-link></p>
          </div>
        </form>
        
        <div v-if="error" class="error-alert">
          {{ error }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, ref, computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'Login',
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const form = reactive({
      username: '',
      password: '',
      rememberMe: false
    })
    
    const errors = reactive({})
    const loading = ref(false)
    const error = computed(() => store.state.error)
    
    const validateForm = () => {
      const newErrors = {}
      
      if (!form.username.trim()) {
        newErrors.username = '请输入用户名或邮箱'
      }
      
      if (!form.password.trim()) {
        newErrors.password = '请输入密码'
      } else if (form.password.length < 6) {
        newErrors.password = '密码长度至少6位'
      }
      
      Object.assign(errors, newErrors)
      return Object.keys(newErrors).length === 0
    }
    
    const handleLogin = async () => {
      // 清除之前的错误
      Object.keys(errors).forEach(key => delete errors[key])
      store.dispatch('clearError')
      
      if (!validateForm()) {
        return
      }
      
      loading.value = true
      
      try {
        await store.dispatch('auth/login', {
          username: form.username,
          password: form.password
        })
        
        // 登录成功，跳转到首页或之前的页面
        const redirect = router.currentRoute.value.query.redirect || '/'
        router.push(redirect)
      } catch (error) {
        console.error('登录失败:', error)
        store.dispatch('setError', error.response?.data?.message || '登录失败，请检查用户名和密码')
      } finally {
        loading.value = false
      }
    }
    
    return {
      form,
      errors,
      loading,
      error,
      handleLogin
    }
  }
}
</script>

<style scoped>
.login-page {
  min-height: calc(100vh - 140px);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 40px 20px;
}

.container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 28px;
}

.login-header p {
  color: #7f8c8d;
  margin: 0;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #2c3e50;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
}

.form-control.error {
  border-color: #e74c3c;
}

.error-message {
  color: #e74c3c;
  font-size: 14px;
  margin-top: 5px;
  display: block;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #7f8c8d;
  font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 8px;
}

.forgot-link {
  color: #3498db;
  text-decoration: none;
  font-size: 14px;
}

.forgot-link:hover {
  text-decoration: underline;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2980b9;
}

.btn-primary:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.btn-full {
  width: 100%;
}

.login-footer {
  text-align: center;
  margin-top: 25px;
  padding-top: 25px;
  border-top: 1px solid #e1e8ed;
}

.login-footer p {
  color: #7f8c8d;
  margin: 0;
}

.login-footer a {
  color: #3498db;
  text-decoration: none;
}

.login-footer a:hover {
  text-decoration: underline;
}

.error-alert {
  background-color: #fdf2f2;
  color: #e74c3c;
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 20px;
  border: 1px solid #fecaca;
}

@media (max-width: 480px) {
  .login-card {
    padding: 30px 20px;
  }
  
  .login-header h2 {
    font-size: 24px;
  }
}
</style>