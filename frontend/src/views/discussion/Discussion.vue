<template>
  <div class="discussion-page">
    <div class="container">
      <!-- 面包屑导航 -->
      <nav class="breadcrumb">
        <router-link to="/" class="breadcrumb-item">首页</router-link>
        <span class="breadcrumb-separator">/</span>
        <router-link :to="`/course/${courseId}`" class="breadcrumb-item">课程详情</router-link>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-current">讨论详情</span>
      </nav>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner">🔄</div>
        <p>加载讨论内容中...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state">
        <h3>加载失败</h3>
        <p>{{ error }}</p>
        <button class="btn btn-primary" @click="loadDiscussion">重新加载</button>
      </div>

      <!-- 讨论内容 -->
      <div v-else-if="discussion" class="discussion-content">
        <!-- 讨论主题 -->
        <div class="discussion-topic">
          <div class="topic-header">
            <h1 class="topic-title">{{ discussion.title }}</h1>
            <div class="topic-meta">
              <div class="author-info">
                <img
                  :src="discussion.avatarUrl || '/default-avatar.png'"
                  :alt="discussion.nickname"
                  class="author-avatar"
                />
                <div class="author-details">
                  <span class="author-name">{{ discussion.nickname || discussion.username }}</span>
                  <span class="author-role" :class="discussion.userRole?.toLowerCase()">
                    {{ getRoleText(discussion.userRole) }}
                  </span>
                </div>
              </div>
              <div class="topic-stats">
                <span class="created-time">{{ formatTime(discussion.createdAt) }}</span>
                <span class="view-count">{{ discussion.viewCount || 0 }} 浏览</span>
                <span class="reply-count">{{ discussion.replyCount || 0 }} 回复</span>
              </div>
            </div>
          </div>

          <div class="topic-content">
            <div class="content-text" v-html="formatContent(discussion.content)"></div>
          </div>

          <div class="topic-actions">
            <button
              class="action-btn like-btn"
              :class="{ active: discussion.isLiked }"
              @click="toggleLike"
            >
              <span class="icon">👍</span>
              <span>{{ discussion.likeCount || 0 }}</span>
            </button>
            <button class="action-btn reply-btn" @click="showReplyForm = true">
              <span class="icon">💬</span>
              <span>回复</span>
            </button>
          </div>
        </div>

        <!-- 回复列表 -->
        <div class="replies-section">
          <div class="replies-header">
            <h3>全部回复 ({{ replies.length }})</h3>
            <div class="sort-options">
              <button
                class="sort-btn"
                :class="{ active: sortBy === 'created_at' }"
                @click="changeSortBy('created_at')"
              >
                时间排序
              </button>
              <button
                class="sort-btn"
                :class="{ active: sortBy === 'like_count' }"
                @click="changeSortBy('like_count')"
              >
                热度排序
              </button>
            </div>
          </div>

          <!-- 回复表单 -->
          <div v-if="showReplyForm" class="reply-form">
            <div class="form-header">
              <h4>发表回复</h4>
              <button class="close-btn" @click="closeReplyForm">&times;</button>
            </div>
            <form @submit.prevent="submitReply">
              <textarea
                v-model="replyForm.content"
                class="reply-input"
                placeholder="请输入您的回复内容..."
                rows="4"
                required
              ></textarea>
              <div class="form-actions">
                <button type="button" class="btn btn-secondary" @click="closeReplyForm">
                  取消
                </button>
                <button type="submit" class="btn btn-primary" :disabled="submittingReply">
                  {{ submittingReply ? '发布中...' : '发布回复' }}
                </button>
              </div>
            </form>
          </div>

          <!-- 回复列表 -->
          <div class="replies-list">
            <div v-if="replies.length === 0" class="empty-replies">
              <p>暂无回复，快来发表第一个回复吧！</p>
            </div>
            <div v-else>
              <div
                v-for="reply in replies"
                :key="reply.id"
                class="reply-item"
              >
                <div class="reply-author">
                  <img
                    :src="reply.avatarUrl || '/default-avatar.png'"
                    :alt="reply.nickname"
                    class="author-avatar"
                  />
                  <div class="author-info">
                    <span class="author-name">{{ reply.nickname || reply.username }}</span>
                    <span class="author-role" :class="reply.userRole?.toLowerCase()">
                      {{ getRoleText(reply.userRole) }}
                    </span>
                    <span class="reply-time">{{ formatTime(reply.createdAt) }}</span>
                  </div>
                </div>
                <div class="reply-content">
                  <div class="content-text" v-html="formatContent(reply.content)"></div>
                </div>
                <div class="reply-actions">
                  <button
                    class="action-btn like-btn"
                    :class="{ active: reply.isLiked }"
                    @click="toggleReplyLike(reply.id)"
                  >
                    <span class="icon">👍</span>
                    <span>{{ reply.likeCount || 0 }}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载更多 -->
          <div v-if="hasMoreReplies" class="load-more">
            <button class="btn btn-outline" @click="loadMoreReplies" :disabled="loadingReplies">
              {{ loadingReplies ? '加载中...' : '加载更多回复' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <h3>讨论不存在</h3>
        <p>该讨论可能已被删除或不存在</p>
        <router-link :to="`/course/${courseId}`" class="btn btn-primary">
          返回课程页面
        </router-link>
      </div>
    </div>
  </div>
</template>
<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'

export default {
  name: 'DiscussionPage',
  setup() {
    const store = useStore()
    const route = useRoute()
    const router = useRouter()

    const loading = ref(true)
    const error = ref(null)
    const loadingReplies = ref(false)
    const submittingReply = ref(false)
    const showReplyForm = ref(false)
    const sortBy = ref('created_at')
    const currentPage = ref(1)
    const hasMoreReplies = ref(false)

    const courseId = computed(() => route.params.courseId)
    const discussionId = computed(() => route.query.discussionId)

    const discussion = computed(() => store.getters['discussion/currentDiscussion'])
    const replies = computed(() => store.getters['discussion/replies'])

    // 回复表单数据
    const replyForm = ref({
      content: ''
    })

    // 获取角色文本
    const getRoleText = (role) => {
      const roleMap = {
        'ADMIN': '管理员',
        'TEACHER': '讲师',
        'STUDENT': '学员'
      }
      return roleMap[role] || '用户'
    }

    // 格式化时间
    const formatTime = (timeString) => {
      if (!timeString) return ''
      const date = new Date(timeString)
      const now = new Date()
      const diff = now - date

      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`

      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    // 格式化内容（简单的换行处理）
    const formatContent = (content) => {
      if (!content) return ''
      return content.replace(/\n/g, '<br>')
    }

    // 加载讨论详情
    const loadDiscussion = async () => {
      if (!discussionId.value) {
        error.value = '缺少讨论ID参数'
        loading.value = false
        return
      }

      try {
        loading.value = true
        error.value = null

        // 加载讨论详情
        await store.dispatch('discussion/fetchDiscussion', discussionId.value)

        // 加载回复列表
        await loadReplies()

      } catch (err) {
        console.error('加载讨论失败:', err)
        error.value = err.response?.data?.message || '加载讨论失败'
      } finally {
        loading.value = false
      }
    }

    // 加载回复列表
    const loadReplies = async (page = 1) => {
      try {
        loadingReplies.value = true

        const response = await store.dispatch('discussion/fetchReplies', {
          topicId: discussionId.value,
          page,
          size: 10,
          sortBy: sortBy.value
        })

        // 检查是否还有更多回复
        const data = response.data.data
        hasMoreReplies.value = data && data.current < data.pages

      } catch (err) {
        console.error('加载回复失败:', err)
        store.dispatch('setError', '加载回复失败')
      } finally {
        loadingReplies.value = false
      }
    }

    // 加载更多回复
    const loadMoreReplies = async () => {
      currentPage.value += 1
      await loadReplies(currentPage.value)
    }

    // 切换排序方式
    const changeSortBy = async (newSortBy) => {
      if (sortBy.value === newSortBy) return

      sortBy.value = newSortBy
      currentPage.value = 1
      await loadReplies(1)
    }

    // 切换点赞
    const toggleLike = async () => {
      try {
        await store.dispatch('discussion/likeDiscussion', discussionId.value)
      } catch (err) {
        console.error('点赞失败:', err)
        store.dispatch('setError', '操作失败，请重试')
      }
    }

    // 切换回复点赞
    const toggleReplyLike = async (replyId) => {
      try {
        await store.dispatch('discussion/likeReply', replyId)
        // 重新加载回复列表以更新点赞数
        await loadReplies(currentPage.value)
        store.dispatch('setSuccess', '点赞成功')
      } catch (err) {
        console.error('点赞回复失败:', err)
        store.dispatch('setError', err.response?.data?.message || '操作失败，请重试')
      }
    }

    // 关闭回复表单
    const closeReplyForm = () => {
      showReplyForm.value = false
      replyForm.value.content = ''
    }

    // 提交回复
    const submitReply = async () => {
      if (!replyForm.value.content.trim()) {
        store.dispatch('setError', '请输入回复内容')
        return
      }

      try {
        submittingReply.value = true

        const replyData = {
          topicId: discussionId.value,
          content: replyForm.value.content.trim()
        }

        await store.dispatch('discussion/createReply', replyData)

        // 回复成功，关闭表单并刷新回复列表
        closeReplyForm()
        await loadReplies(1)

        store.dispatch('setSuccess', '回复发布成功！')
      } catch (err) {
        console.error('发布回复失败:', err)
        store.dispatch('setError', err.response?.data?.message || '发布回复失败，请重试')
      } finally {
        submittingReply.value = false
      }
    }

    // 监听路由参数变化
    watch([courseId, discussionId], () => {
      if (courseId.value && discussionId.value) {
        loadDiscussion()
      }
    }, { immediate: true })

    onMounted(() => {
      if (courseId.value && discussionId.value) {
        loadDiscussion()
      }
    })

    return {
      loading,
      error,
      loadingReplies,
      submittingReply,
      showReplyForm,
      sortBy,
      hasMoreReplies,
      courseId,
      discussionId,
      discussion,
      replies,
      replyForm,
      getRoleText,
      formatTime,
      formatContent,
      loadDiscussion,
      loadMoreReplies,
      changeSortBy,
      toggleLike,
      toggleReplyLike,
      closeReplyForm,
      submitReply
    }
  }
}
</script>
<style scoped>
.discussion-page {
  min-height: 100vh;
  background: #f8f9fa;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

/* 面包屑导航 */
.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  font-size: 14px;
}

.breadcrumb-item {
  color: #007bff;
  text-decoration: none;
}

.breadcrumb-item:hover {
  text-decoration: underline;
}

.breadcrumb-separator {
  margin: 0 10px;
  color: #6c757d;
}

.breadcrumb-current {
  color: #6c757d;
}

/* 加载和错误状态 */
.loading-state, .error-state, .empty-state {
  text-align: center;
  padding: 60px 0;
}

.loading-spinner {
  font-size: 24px;
  animation: spin 2s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 讨论内容 */
.discussion-content {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 讨论主题 */
.discussion-topic {
  padding: 30px;
  border-bottom: 1px solid #eee;
}

.topic-header {
  margin-bottom: 20px;
}

.topic-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 15px;
  line-height: 1.4;
}

.topic-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.author-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.author-name {
  font-weight: 500;
  color: #2c3e50;
}

.author-role {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  color: white;
}

.author-role.admin {
  background: #dc3545;
}

.author-role.teacher {
  background: #28a745;
}

.author-role.student {
  background: #007bff;
}

.topic-stats {
  display: flex;
  gap: 15px;
  font-size: 14px;
  color: #6c757d;
}

.topic-content {
  margin: 20px 0;
}

.content-text {
  font-size: 16px;
  line-height: 1.6;
  color: #2c3e50;
}

.topic-actions {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
}

.action-btn:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.action-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

/* 回复区域 */
.replies-section {
  padding: 30px;
}

.replies-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.replies-header h3 {
  color: #2c3e50;
  margin: 0;
}

.sort-options {
  display: flex;
  gap: 10px;
}

.sort-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.sort-btn:hover {
  border-color: #007bff;
}

.sort-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

/* 回复表单 */
.reply-form {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.form-header h4 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #2c3e50;
}

.reply-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
  margin-bottom: 15px;
  box-sizing: border-box;
}

.reply-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-outline {
  background: white;
  color: #007bff;
  border: 1px solid #007bff;
}

.btn-outline:hover {
  background: #007bff;
  color: white;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 回复列表 */
.replies-list {
  margin-bottom: 25px;
}

.empty-replies {
  text-align: center;
  padding: 40px 0;
  color: #6c757d;
}

.reply-item {
  border-bottom: 1px solid #eee;
  padding: 20px 0;
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-author {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.reply-content {
  margin: 12px 0;
  padding-left: 52px;
}

.reply-actions {
  padding-left: 52px;
}

.author-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.reply-time {
  font-size: 12px;
  color: #6c757d;
}

/* 加载更多 */
.load-more {
  text-align: center;
  margin-top: 25px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .topic-meta {
    flex-direction: column;
    align-items: flex-start;
  }

  .topic-stats {
    flex-wrap: wrap;
  }

  .replies-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .reply-content,
  .reply-actions {
    padding-left: 0;
    margin-left: 52px;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style>