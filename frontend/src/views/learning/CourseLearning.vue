<template>
  <div class="course-learning">
    <div class="container">
      <div v-if="loading" class="loading">
        加载课程内容中...
      </div>

      <div v-else-if="course" class="learning-content">
        <!-- 课程头部信息 -->
        <div class="course-header">
          <div class="breadcrumb">
            <router-link to="/learning">学习中心</router-link>
            <span> / </span>
            <span>{{ course.title }}</span>
          </div>

          <div class="course-info">
            <h1>{{ course.title }}</h1>
            <div class="course-meta">
              <span class="teacher">👨‍🏫 {{ course.teacherName }}</span>
              <span class="difficulty">📊 {{ getDifficultyText(course.difficultyLevel) }}</span>
              <span class="progress">📈 进度: {{ Math.round(learningProgress) }}%</span>
            </div>
          </div>

          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: learningProgress + '%' }"></div>
          </div>
        </div>

        <!-- 课程内容区域 -->
        <div class="learning-main">
          <!-- 左侧：章节列表 -->
          <div class="chapters-sidebar">
            <h3>课程章节</h3>
            <div class="chapters-list">
              <div
                v-for="(chapter, index) in chapters"
                :key="chapter.id"
                class="chapter-item"
                :class="{ 'active': currentChapter?.id === chapter.id, 'completed': chapter.completed }"
                @click="selectChapter(chapter)"
              >
                <div class="chapter-number">{{ index + 1 }}</div>
                <div class="chapter-info">
                  <h4>{{ chapter.title }}</h4>
                  <p>{{ chapter.description }}</p>
                  <div class="chapter-meta">
                    <span class="duration">⏱️ {{ chapter.duration || '15分钟' }}</span>
                    <span class="type">{{ getContentTypeText(chapter.contentType) }}</span>
                  </div>
                </div>
                <div class="chapter-status">
                  <span v-if="chapter.completed" class="completed-icon">✅</span>
                  <span v-else class="pending-icon">⏳</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：学习内容 -->
          <div class="content-area">
            <div v-if="currentChapter" class="chapter-content">
              <div class="chapter-header">
                <h2>{{ currentChapter.title }}</h2>
                <p>{{ currentChapter.description }}</p>
              </div>

              <!-- 视频内容 -->
              <div v-if="currentChapter.contentType === 'VIDEO'" class="video-content">
                <div class="video-placeholder">
                  <div class="video-icon">🎥</div>
                  <h3>视频内容</h3>
                  <p>{{ currentChapter.title }}</p>
                  <button class="play-btn" @click="playVideo">▶️ 播放视频</button>
                </div>
              </div>

              <!-- 文档内容 -->
              <div v-else-if="currentChapter.contentType === 'DOCUMENT'" class="document-content">
                <div class="document-placeholder">
                  <div class="document-icon">📄</div>
                  <h3>文档内容</h3>
                  <p>{{ currentChapter.content || '这里是课程文档内容...' }}</p>
                  <div class="document-actions">
                    <button class="btn btn-outline">📖 阅读文档</button>
                    <button class="btn btn-outline">💾 下载资料</button>
                  </div>
                </div>
              </div>

              <!-- 测验内容 -->
              <div v-else-if="currentChapter.contentType === 'QUIZ'" class="quiz-content">
                <div class="quiz-placeholder">
                  <div class="quiz-icon">❓</div>
                  <h3>章节测验</h3>
                  <p>完成测验来检验你的学习成果</p>
                  <button class="btn btn-primary" @click="startQuiz">开始测验</button>
                </div>
              </div>

              <!-- 章节操作 -->
              <div class="chapter-actions">
                <button
                  class="btn btn-outline"
                  @click="previousChapter"
                  :disabled="!hasPreviousChapter"
                >
                  ⬅️ 上一章
                </button>

                <button
                  class="btn btn-primary"
                  @click="completeChapter"
                  :disabled="currentChapter.completed"
                >
                  {{ currentChapter.completed ? '✅ 已完成' : '✅ 标记完成' }}
                </button>

                <button
                  class="btn btn-outline"
                  @click="nextChapter"
                  :disabled="!hasNextChapter"
                >
                  下一章 ➡️
                </button>
              </div>
            </div>

            <div v-else class="no-chapter">
              <div class="empty-state">
                <h3>请选择一个章节开始学习</h3>
                <p>从左侧章节列表中选择要学习的内容</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="error-state">
        <h3>课程加载失败</h3>
        <p>无法加载课程内容，请稍后重试</p>
        <button class="btn btn-primary" @click="loadCourse">重新加载</button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import learningApi from '@/api/learning'

export default {
  name: 'CourseLearning',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const store = useStore()

    const loading = ref(true)
    const course = ref(null)
    const chapters = ref([])
    const currentChapter = ref(null)
    const learningProgress = ref(0)

    const courseId = computed(() => route.params.id)

    // 模拟章节数据
    const mockChapters = [
      {
        id: 1,
        title: '课程介绍',
        description: '了解课程目标和学习路径',
        contentType: 'VIDEO',
        duration: '10分钟',
        completed: false,
        content: '欢迎来到本课程！'
      },
      {
        id: 2,
        title: '基础概念',
        description: '掌握核心概念和基础知识',
        contentType: 'DOCUMENT',
        duration: '20分钟',
        completed: false,
        content: '这里是基础概念的详细说明...'
      },
      {
        id: 3,
        title: '实践练习',
        description: '通过实际操作加深理解',
        contentType: 'VIDEO',
        duration: '30分钟',
        completed: false,
        content: '实践是最好的学习方式'
      },
      {
        id: 4,
        title: '章节测验',
        description: '检验学习成果',
        contentType: 'QUIZ',
        duration: '15分钟',
        completed: false,
        content: '测试你对本章内容的掌握程度'
      }
    ]

    const hasPreviousChapter = computed(() => {
      if (!currentChapter.value) return false
      const currentIndex = chapters.value.findIndex(c => c.id === currentChapter.value.id)
      return currentIndex > 0
    })

    const hasNextChapter = computed(() => {
      if (!currentChapter.value) return false
      const currentIndex = chapters.value.findIndex(c => c.id === currentChapter.value.id)
      return currentIndex < chapters.value.length - 1
    })

    const getDifficultyText = (level) => {
      const difficultyMap = {
        'BEGINNER': '初级',
        'INTERMEDIATE': '中级',
        'ADVANCED': '高级'
      }
      return difficultyMap[level] || level
    }

    const getContentTypeText = (type) => {
      const typeMap = {
        'VIDEO': '🎥 视频',
        'DOCUMENT': '📄 文档',
        'QUIZ': '❓ 测验'
      }
      return typeMap[type] || type
    }

    const loadCourse = async () => {
      loading.value = true
      try {
        // 获取课程信息
        await store.dispatch('course/fetchCourse', courseId.value)
        course.value = store.getters['course/currentCourse']

        // 获取真实的章节数据和学习进度
        await loadChaptersAndProgress()

      } catch (error) {
        console.error('加载课程失败:', error)
      } finally {
        loading.value = false
      }
    }

    const loadChaptersAndProgress = async () => {
      try {
        // 首先设置基础章节数据（模拟数据作为基础结构）
        chapters.value = mockChapters.map(chapter => ({
          ...chapter,
          completed: false // 默认未完成
        }))

        // 获取用户的学习进度
        const progressResponse = await learningApi.getCourseProgress(courseId.value)
        if (progressResponse.data.success) {
          const progressList = progressResponse.data.data

          // 根据后端进度数据更新章节完成状态
          progressList.forEach(progress => {
            const chapter = chapters.value.find(c => c.id === progress.chapterId)
            if (chapter && progress.completed) {
              chapter.completed = true
            }
          })
        }

        // 默认选择第一章
        if (chapters.value.length > 0) {
          currentChapter.value = chapters.value[0]
        }

        // 计算学习进度
        const completedChapters = chapters.value.filter(c => c.completed).length
        learningProgress.value = (completedChapters / chapters.value.length) * 100

      } catch (error) {
        console.error('加载章节和进度失败:', error)
        // 如果获取进度失败，使用模拟数据
        chapters.value = mockChapters
        if (chapters.value.length > 0) {
          currentChapter.value = chapters.value[0]
        }
        learningProgress.value = 0
      }
    }

    const selectChapter = (chapter) => {
      currentChapter.value = chapter
    }

    const previousChapter = () => {
      const currentIndex = chapters.value.findIndex(c => c.id === currentChapter.value.id)
      if (currentIndex > 0) {
        currentChapter.value = chapters.value[currentIndex - 1]
      }
    }

    const nextChapter = () => {
      const currentIndex = chapters.value.findIndex(c => c.id === currentChapter.value.id)
      if (currentIndex < chapters.value.length - 1) {
        currentChapter.value = chapters.value[currentIndex + 1]
      }
    }

    const completeChapter = async () => {
      if (!currentChapter.value || currentChapter.value.completed) {
        return
      }

      try {
        // 调用后端API标记章节完成
        await learningApi.completeChapter({
          courseId: courseId.value,
          chapterId: currentChapter.value.id
        })

        // 重新加载章节进度数据，确保与后端同步
        await loadChaptersAndProgress()

        // 显示成功消息
        store.dispatch('setSuccess', '章节完成成功！')

        // 自动跳转到下一章
        if (hasNextChapter.value) {
          setTimeout(() => {
            nextChapter()
          }, 1000)
        }
      } catch (error) {
        console.error('完成章节失败:', error)
        store.dispatch('setError', error.response?.data?.message || '完成章节失败，请稍后重试')
      }
    }

    const playVideo = () => {
      alert('视频播放功能待实现')
    }

    const startQuiz = () => {
      alert('测验功能待实现')
    }

    onMounted(() => {
      loadCourse()
    })

    return {
      loading,
      course,
      chapters,
      currentChapter,
      learningProgress,
      hasPreviousChapter,
      hasNextChapter,
      getDifficultyText,
      getContentTypeText,
      loadCourse,
      selectChapter,
      previousChapter,
      nextChapter,
      completeChapter,
      playVideo,
      startQuiz
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.loading {
  text-align: center;
  padding: 60px 0;
  font-size: 18px;
  color: #7f8c8d;
}

.course-header {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.breadcrumb {
  margin-bottom: 20px;
  color: #7f8c8d;
  font-size: 14px;
}

.breadcrumb a {
  color: #3498db;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.course-info h1 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 28px;
}

.course-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  color: #7f8c8d;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  transition: width 0.3s ease;
}

.learning-main {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 30px;
}

.chapters-sidebar {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  height: fit-content;
}

.chapters-sidebar h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 20px;
}

.chapter-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 10px;
  border: 2px solid transparent;
}

.chapter-item:hover {
  background-color: #f8f9fa;
}

.chapter-item.active {
  background-color: #e3f2fd;
  border-color: #3498db;
}

.chapter-item.completed {
  background-color: #f1f8e9;
}

.chapter-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 15px;
  flex-shrink: 0;
}

.chapter-item.completed .chapter-number {
  background-color: #27ae60;
}

.chapter-info {
  flex: 1;
}

.chapter-info h4 {
  color: #2c3e50;
  margin-bottom: 5px;
  font-size: 16px;
}

.chapter-info p {
  color: #7f8c8d;
  font-size: 14px;
  margin-bottom: 8px;
}

.chapter-meta {
  display: flex;
  gap: 10px;
  font-size: 12px;
  color: #95a5a6;
}

.chapter-status {
  margin-left: 10px;
}

.completed-icon {
  color: #27ae60;
  font-size: 18px;
}

.pending-icon {
  color: #f39c12;
  font-size: 18px;
}

.content-area {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.chapter-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ecf0f1;
}

.chapter-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.chapter-header p {
  color: #7f8c8d;
}

.video-content,
.document-content,
.quiz-content {
  margin-bottom: 30px;
}

.video-placeholder,
.document-placeholder,
.quiz-placeholder {
  text-align: center;
  padding: 60px 30px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #bdc3c7;
}

.video-icon,
.document-icon,
.quiz-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.video-placeholder h3,
.document-placeholder h3,
.quiz-placeholder h3 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.video-placeholder p,
.document-placeholder p,
.quiz-placeholder p {
  color: #7f8c8d;
  margin-bottom: 20px;
}

.play-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.play-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.document-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.chapter-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 30px;
  border-top: 1px solid #ecf0f1;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2980b9;
  transform: translateY(-2px);
}

.btn-primary:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
  transform: none;
}

.btn-outline {
  background-color: transparent;
  color: #3498db;
  border: 2px solid #3498db;
}

.btn-outline:hover:not(:disabled) {
  background-color: #3498db;
  color: white;
}

.btn-outline:disabled {
  color: #bdc3c7;
  border-color: #bdc3c7;
  cursor: not-allowed;
}

.no-chapter {
  text-align: center;
  padding: 60px 30px;
}

.empty-state h3 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.empty-state p {
  color: #7f8c8d;
}

.error-state {
  text-align: center;
  padding: 60px 30px;
}

.error-state h3 {
  color: #e74c3c;
  margin-bottom: 10px;
}

.error-state p {
  color: #7f8c8d;
  margin-bottom: 20px;
}

@media (max-width: 1024px) {
  .learning-main {
    grid-template-columns: 1fr;
  }

  .chapters-sidebar {
    order: 2;
  }

  .content-area {
    order: 1;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .course-header {
    padding: 20px;
  }

  .course-info h1 {
    font-size: 24px;
  }

  .course-meta {
    flex-direction: column;
    gap: 10px;
  }

  .chapters-sidebar,
  .content-area {
    padding: 20px;
  }

  .chapter-actions {
    flex-direction: column;
    gap: 15px;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }
}
</style>