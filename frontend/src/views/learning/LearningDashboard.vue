<template>
  <div class="learning-dashboard">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>学习仪表板</h1>
        <p class="subtitle">掌握您的学习进度和成就</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <div class="spinner"></div>
        <p>正在加载学习数据...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-message">
        <p>{{ error }}</p>
        <button @click="loadDashboardData" class="retry-btn">重试</button>
      </div>

      <!-- 仪表板内容 -->
      <div v-else class="dashboard-content">
        <!-- 统计卡片 -->
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">📚</div>
            <div class="stat-info">
              <h3>{{ dashboardData.totalEnrolledCourses }}</h3>
              <p>已注册课程</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">🎯</div>
            <div class="stat-info">
              <h3>{{ dashboardData.inProgressCourses }}</h3>
              <p>学习中课程</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">✅</div>
            <div class="stat-info">
              <h3>{{ dashboardData.completedCourses }}</h3>
              <p>已完成课程</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">📖</div>
            <div class="stat-info">
              <h3>{{ dashboardData.completedChapters }}</h3>
              <p>已完成章节</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">⏰</div>
            <div class="stat-info">
              <h3>{{ formatLearningHours(dashboardData.totalLearningHours) }}</h3>
              <p>学习时长</p>
            </div>
          </div>
        </div>

        <!-- 最近学习 -->
        <div class="recent-learning">
          <h2>最近学习</h2>
          <div v-if="dashboardData.recentEnrollments && dashboardData.recentEnrollments.length > 0" class="recent-courses">
            <div
              v-for="enrollment in dashboardData.recentEnrollments"
              :key="enrollment.id"
              class="recent-course-card"
              @click="goToCourse(enrollment.courseId)"
            >
              <div class="course-image">
                <img
                  v-if="enrollment.courseCoverImage"
                  :src="enrollment.courseCoverImage"
                  :alt="enrollment.courseTitle"
                  class="cover-image"
                />
                <div v-else class="placeholder-image">
                  📚
                </div>
              </div>
              <div class="course-info">
                <h4>{{ enrollment.courseTitle || `课程 #${enrollment.courseId}` }}</h4>
                <p v-if="enrollment.teacherName" class="teacher-name">讲师: {{ enrollment.teacherName }}</p>
                <p class="enrollment-date">注册时间: {{ formatDate(enrollment.enrolledAt) }}</p>
                <div class="progress-info">
                  <div class="progress-bar">
                    <div
                      class="progress-fill"
                      :style="{ width: enrollment.progressPercentage + '%' }"
                    ></div>
                  </div>
                  <span class="progress-text">{{ enrollment.progressPercentage }}%</span>
                </div>
              </div>
              <div class="course-status">
                <span
                  :class="['status-badge', enrollment.completedAt ? 'completed' : 'in-progress']"
                >
                  {{ enrollment.completedAt ? '已完成' : '学习中' }}
                </span>
                <span v-if="enrollment.difficultyLevel" class="difficulty-badge">
                  {{ getDifficultyText(enrollment.difficultyLevel) }}
                </span>
              </div>
            </div>
          </div>
          <div v-else class="empty-state">
            <p>暂无学习记录</p>
            <router-link to="/courses" class="start-learning-btn">开始学习</router-link>
          </div>
        </div>

        <!-- 学习统计图表 -->
        <div class="learning-stats">
          <h2>学习统计</h2>
          <div class="stats-content">
            <div class="stat-item">
              <label>平均进度:</label>
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: statistics.averageProgress + '%' }"
                ></div>
              </div>
              <span>{{ statistics.averageProgress }}%</span>
            </div>

            <div class="stat-item">
              <label>章节完成率:</label>
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: getChapterCompletionRate() + '%' }"
                ></div>
              </div>
              <span>{{ getChapterCompletionRate() }}%</span>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="quick-actions">
          <h2>快速操作</h2>
          <div class="action-buttons">
            <router-link to="/courses" class="action-btn primary">
              <span class="btn-icon">🔍</span>
              浏览课程
            </router-link>
            <router-link to="/learning/statistics" class="action-btn">
              <span class="btn-icon">📊</span>
              详细统计
            </router-link>
            <button @click="refreshData" class="action-btn">
              <span class="btn-icon">🔄</span>
              刷新数据
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'LearningDashboard',
  data() {
    return {
      loading: true,
      error: null,
      dashboardData: {
        totalEnrolledCourses: 0,
        inProgressCourses: 0,
        completedCourses: 0,
        completedChapters: 0,
        totalLearningHours: 0,
        recentEnrollments: []
      },
      statistics: {
        averageProgress: 0,
        totalCourses: 0,
        totalChapters: 0,
        completedChapters: 0
      }
    }
  },
  computed: {
    ...mapState('auth', ['user'])
  },
  async mounted() {
    await this.loadDashboardData()
  },
  methods: {
    ...mapActions('learning', ['getDashboard', 'getStatistics']),

    async loadDashboardData() {
      this.loading = true
      this.error = null

      try {
        // 加载仪表板数据
        const dashboardResponse = await this.getDashboard()
        if (dashboardResponse.success) {
          this.dashboardData = dashboardResponse.data
        }

        // 加载统计数据
        const statsResponse = await this.getStatistics()
        if (statsResponse.success) {
          this.statistics = statsResponse.data
        }

      } catch (error) {
        console.error('加载仪表板数据失败:', error)
        this.error = '加载数据失败，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    async refreshData() {
      await this.loadDashboardData()
    },

    formatLearningHours(hours) {
      if (hours < 1) {
        return Math.round(hours * 60) + '分钟'
      }
      return hours.toFixed(1) + '小时'
    },

    formatDate(dateString) {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    },

    getChapterCompletionRate() {
      if (this.statistics.totalChapters === 0) return 0
      return Math.round((this.statistics.completedChapters / this.statistics.totalChapters) * 100)
    },

    goToCourse(courseId) {
      this.$router.push(`/course/${courseId}`)
    },

    getDifficultyText(level) {
      const difficultyMap = {
        'BEGINNER': '初级',
        'INTERMEDIATE': '中级',
        'ADVANCED': '高级'
      }
      return difficultyMap[level] || level
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 2.5rem;
}

.subtitle {
  color: #7f8c8d;
  font-size: 1.1rem;
}

/* 加载和错误状态 */
.loading {
  text-align: center;
  padding: 60px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  text-align: center;
  padding: 40px 20px;
  color: #e74c3c;
}

.retry-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 15px;
}

.retry-btn:hover {
  background: #2980b9;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  border-radius: 10px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stat-icon {
  font-size: 2.5rem;
  margin-right: 20px;
}

.stat-info h3 {
  font-size: 2rem;
  color: #2c3e50;
  margin: 0 0 5px 0;
}

.stat-info p {
  color: #7f8c8d;
  margin: 0;
  font-size: 0.9rem;
}

/* 最近学习 */
.recent-learning {
  background: white;
  border-radius: 10px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.recent-learning h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.recent-courses {
  display: grid;
  gap: 15px;
}

.recent-course-card {
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  gap: 15px;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
}

.recent-course-card:hover {
  border-color: #3498db;
  background: #f8f9fa;
}

.course-image {
  flex-shrink: 0;
  width: 80px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  width: 100%;
  height: 100%;
  background: #ecf0f1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.course-info {
  flex: 1;
}

.course-info h4 {
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.teacher-name {
  color: #3498db;
  font-size: 0.9rem;
  margin: 0 0 5px 0;
}

.enrollment-date {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin: 0 0 10px 0;
}

.course-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-end;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  width: 120px;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9rem;
  color: #7f8c8d;
  min-width: 35px;
}

.status-badge {
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.in-progress {
  background: #fff3cd;
  color: #856404;
}

.difficulty-badge {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  background: #e9ecef;
  color: #6c757d;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;
}

.start-learning-btn {
  display: inline-block;
  background: #3498db;
  color: white;
  padding: 12px 24px;
  border-radius: 6px;
  text-decoration: none;
  margin-top: 15px;
  transition: background 0.2s;
}

.start-learning-btn:hover {
  background: #2980b9;
}

/* 学习统计 */
.learning-stats {
  background: white;
  border-radius: 10px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.learning-stats h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.stats-content {
  display: grid;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-item label {
  min-width: 100px;
  color: #2c3e50;
  font-weight: 500;
}

.stat-item .progress-bar {
  flex: 1;
  max-width: 200px;
}

.stat-item span {
  min-width: 50px;
  text-align: right;
  color: #7f8c8d;
}

/* 快速操作 */
.quick-actions {
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.quick-actions h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 6px;
  text-decoration: none;
  border: 1px solid #ddd;
  background: white;
  color: #2c3e50;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  border-color: #3498db;
  color: #3498db;
}

.action-btn.primary {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.action-btn.primary:hover {
  background: #2980b9;
  border-color: #2980b9;
  color: white;
}

.btn-icon {
  font-size: 1.1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .recent-course-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-btn {
    justify-content: center;
  }
}
</style>