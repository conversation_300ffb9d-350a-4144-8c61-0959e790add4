<template>
  <div class="home">
    <div class="container">
      <!-- 英雄区域 -->
      <section class="hero">
        <div class="hero-content">
          <h1 class="hero-title">欢迎来到在线学习平台</h1>
          <p class="hero-subtitle">
            基于微服务架构的现代化学习平台，为您提供个性化的学习体验
          </p>
          <div class="hero-actions">
            <router-link to="/courses" class="btn btn-primary btn-large">
              开始学习
            </router-link>
            <router-link to="/register" class="btn btn-outline btn-large" v-if="!isAuthenticated">
              立即注册
            </router-link>
          </div>
        </div>
        <div class="hero-image">
          <div class="placeholder-image">
            📚 学习平台
          </div>
        </div>
      </section>
      
      <!-- 特色功能 -->
      <section class="features">
        <h2 class="section-title">平台特色</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🎯</div>
            <h3>个性化推荐</h3>
            <p>基于AI算法的智能课程推荐，为您量身定制学习路径</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📊</div>
            <h3>学习进度跟踪</h3>
            <p>实时跟踪学习进度，可视化展示学习成果和统计数据</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">💬</div>
            <h3>互动讨论</h3>
            <p>与同学和老师实时交流，共同探讨学习中的问题</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🏆</div>
            <h3>成就系统</h3>
            <p>完成学习目标获得成就徽章，激励持续学习</p>
          </div>
        </div>
      </section>
      
      <!-- 热门课程 -->
      <section class="popular-courses" v-if="popularCourses.length > 0">
        <h2 class="section-title">热门课程</h2>
        <div class="courses-grid">
          <div 
            v-for="course in popularCourses" 
            :key="course.id" 
            class="course-card"
            @click="goToCourse(course.id)"
          >
            <div class="course-image">
              <img :src="course.coverImage || '/placeholder-course.jpg'" :alt="course.title">
            </div>
            <div class="course-info">
              <h3 class="course-title">{{ course.title }}</h3>
              <p class="course-description">{{ course.description }}</p>
              <div class="course-meta">
                <span class="course-level">{{ course.difficultyLevel }}</span>
                <span class="course-students">{{ course.studentsCount || 0 }}人学习</span>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      <!-- 统计数据 -->
      <section class="stats">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">1000+</div>
            <div class="stat-label">优质课程</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">50000+</div>
            <div class="stat-label">学习用户</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">500+</div>
            <div class="stat-label">专业讲师</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">98%</div>
            <div class="stat-label">满意度</div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import { computed, onMounted, ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'Home',
  setup() {
    const store = useStore()
    const router = useRouter()
    const popularCourses = ref([])
    
    const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])
    
    const goToCourse = (courseId) => {
      router.push(`/course/${courseId}`)
    }
    
    const fetchPopularCourses = async () => {
      try {
        // 获取热门课程数据
        const response = await store.dispatch('course/fetchCourses', { 
          limit: 6, 
          sort: 'popular' 
        })
        popularCourses.value = response.data || []
      } catch (error) {
        console.error('获取热门课程失败:', error)
      }
    }
    
    onMounted(() => {
      fetchPopularCourses()
    })
    
    return {
      isAuthenticated,
      popularCourses,
      goToCourse
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  padding: 60px 0;
}

.hero-title {
  font-size: 48px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 18px;
  color: #7f8c8d;
  margin-bottom: 30px;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 20px;
}

.btn {
  padding: 12px 24px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s;
  border: 2px solid transparent;
  display: inline-block;
  text-align: center;
}

.btn-large {
  padding: 15px 30px;
  font-size: 16px;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover {
  background-color: #2980b9;
}

.btn-outline {
  color: #3498db;
  border-color: #3498db;
}

.btn-outline:hover {
  background-color: #3498db;
  color: white;
}

.placeholder-image {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  color: white;
  font-size: 48px;
  font-weight: bold;
}

.section-title {
  text-align: center;
  font-size: 36px;
  color: #2c3e50;
  margin-bottom: 50px;
}

.features {
  padding: 80px 0;
  background-color: #f8f9fa;
  margin: 0 -20px;
}

.features .container {
  padding: 0 40px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.feature-card {
  background: white;
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: transform 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.feature-card h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 20px;
}

.feature-card p {
  color: #7f8c8d;
  line-height: 1.6;
}

.popular-courses {
  padding: 80px 0;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.course-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: transform 0.3s;
  cursor: pointer;
}

.course-card:hover {
  transform: translateY(-5px);
}

.course-image {
  height: 200px;
  overflow: hidden;
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-info {
  padding: 20px;
}

.course-title {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 18px;
}

.course-description {
  color: #7f8c8d;
  margin-bottom: 15px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.course-level {
  background-color: #e74c3c;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
}

.course-students {
  color: #7f8c8d;
}

.stats {
  padding: 60px 0;
  background-color: #2c3e50;
  margin: 0 -20px;
}

.stats .container {
  padding: 0 40px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
}

.stat-item {
  text-align: center;
  color: white;
}

.stat-number {
  font-size: 48px;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 18px;
  color: #ecf0f1;
}

@media (max-width: 768px) {
  .hero {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .hero-title {
    font-size: 36px;
  }
  
  .hero-actions {
    justify-content: center;
  }
  
  .features-grid,
  .courses-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>