<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>CORS 详细测试</h1>
    
    <div class="test-section">
        <h3>1. 原生 fetch 测试</h3>
        <button onclick="testFetch()">测试 Fetch API</button>
        <div id="fetch-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. XMLHttpRequest 测试</h3>
        <button onclick="testXHR()">测试 XMLHttpRequest</button>
        <div id="xhr-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Axios 测试</h3>
        <button onclick="testAxios()">测试 Axios</button>
        <div id="axios-result" class="result"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        async function testFetch() {
            showResult('fetch-result', '正在测试 Fetch API...', 'info');
            
            try {
                const response = await fetch('http://localhost:8090/api/user/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        usernameOrEmail: 'testlearning',
                        password: '123456'
                    })
                });
                
                const data = await response.json();
                showResult('fetch-result', `Fetch 成功！\n\n状态码: ${response.status}\n\n响应数据:\n${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                showResult('fetch-result', `Fetch 失败！\n\n错误信息:\n${error.message}\n\n错误类型: ${error.name}`, 'error');
                console.error('Fetch error:', error);
            }
        }

        function testXHR() {
            showResult('xhr-result', '正在测试 XMLHttpRequest...', 'info');
            
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'http://localhost:8090/api/user/login', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.withCredentials = true;
            
            xhr.onload = function() {
                if (xhr.status === 200) {
                    const data = JSON.parse(xhr.responseText);
                    showResult('xhr-result', `XHR 成功！\n\n状态码: ${xhr.status}\n\n响应数据:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('xhr-result', `XHR 失败！\n\n状态码: ${xhr.status}\n\n响应文本: ${xhr.responseText}`, 'error');
                }
            };
            
            xhr.onerror = function() {
                showResult('xhr-result', `XHR 网络错误！\n\n状态码: ${xhr.status}\n\n状态文本: ${xhr.statusText}`, 'error');
            };
            
            xhr.send(JSON.stringify({
                usernameOrEmail: 'testlearning',
                password: '123456'
            }));
        }

        async function testAxios() {
            showResult('axios-result', '正在测试 Axios...', 'info');
            
            try {
                const axiosInstance = axios.create({
                    baseURL: 'http://localhost:8090',
                    timeout: 10000,
                    withCredentials: true,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const response = await axiosInstance.post('/api/user/login', {
                    usernameOrEmail: 'testlearning',
                    password: '123456'
                });
                
                showResult('axios-result', `Axios 成功！\n\n状态码: ${response.status}\n\n响应数据:\n${JSON.stringify(response.data, null, 2)}`, 'success');
            } catch (error) {
                let errorMessage = `Axios 失败！\n\n错误信息: ${error.message}`;
                
                if (error.response) {
                    errorMessage += `\n\n响应状态码: ${error.response.status}`;
                    errorMessage += `\n响应数据: ${JSON.stringify(error.response.data, null, 2)}`;
                } else if (error.request) {
                    errorMessage += `\n\n请求已发送但无响应`;
                    errorMessage += `\n请求对象: ${error.request}`;
                } else {
                    errorMessage += `\n\n请求配置错误`;
                }
                
                showResult('axios-result', errorMessage, 'error');
                console.error('Axios error:', error);
            }
        }
    </script>
</body>
</html>
