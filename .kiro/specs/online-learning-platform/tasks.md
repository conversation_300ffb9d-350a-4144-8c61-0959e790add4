# 实施计划

- [x] 1. 项目基础架构搭建
  - 检查和配置Java环境（使用jenv管理，选择合适的JDK版本）
  - 创建父项目和各微服务模块的Maven项目结构
  - 配置SpringCloud Alibaba依赖和版本管理
  - 搭建Nacos服务注册中心
  - 创建MySQL数据库和基础表结构
  - _需求: 6.1, 6.3, 6.4_

- [x] 2. 网关服务实现
  - 创建Spring Cloud Gateway项目
  - 配置路由规则和跨域处理
  - 实现JWT令牌验证过滤器
  - 测试网关路由功能
  - _需求: 6.5_

- [x] 3. 用户服务核心功能
  - [x] 3.1 用户服务基础框架
    - 创建user-service微服务项目
    - 配置数据库连接和JPA实体
    - 实现用户和用户资料的实体类
    - 创建用户Repository接口
    - _需求: 1.1, 1.2_

  - [x] 3.2 用户注册和登录功能
    - 实现用户注册API和密码加密
    - 实现用户登录API和JWT令牌生成
    - 实现用户信息查询和更新API
    - 用curl命令或浏览器测试所有API，记录请求和响应格式
    - 创建API文档，明确定义所有接口的请求参数和返回数据结构
    - _需求: 1.1, 1.2, 1.3, 1.4_

- [ ] 4. 课程服务核心功能
  - [x] 4.1 课程服务基础框架
    - 创建course-service微服务项目
    - 实现课程、章节、分类的实体类
    - 创建课程相关的Repository接口
    - 配置服务注册到Nacos
    - _需求: 2.1, 2.2_

  - [x] 4.2 课程管理功能实现
    - 实现课程创建、编辑、删除API
    - 实现课程列表查询和搜索功能
    - 实现课程章节管理API
    - 实现课程报名功能
    - 用curl命令或浏览器测试所有API，验证数据格式和状态码
    - 更新API文档，确保前端开发时有准确的接口规范
    - _需求: 2.1, 2.2, 2.3, 2.4, 2.5_

- [x] 5. 学习服务核心功能
  - [x] 5.1 学习服务基础框架
    - 创建learning-service微服务项目
    - 实现课程注册、学习进度、测验记录实体类
    - 创建学习相关的Repository接口
    - 配置服务间调用（调用课程服务）
    - _需求: 3.1, 3.2_

  - [x] 5.2 学习进度跟踪功能
    - 实现学习进度记录和更新API
    - 实现学习仪表板数据统计API
    - 实现测验提交和成绩记录功能
    - 实现学习统计和成就通知
    - 用curl命令或浏览器测试所有API，特别注意数据类型和格式
    - 文档化所有接口，包括请求示例和响应示例
    - _需求: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6_

- [x] 6. 推荐服务核心功能
  - [x] 6.1 推荐服务基础框架
    - 创建recommendation-service微服务项目
    - 实现用户行为和推荐结果实体类
    - 创建推荐相关的Repository接口
    - 实现基础的推荐算法逻辑
    - _需求: 4.1, 4.2_

  - [x] 6.2 推荐算法实现
    - 实现基于用户行为的协同过滤推荐
    - 实现基于课程内容的推荐算法
    - 实现推荐结果生成功能
    - 实现推荐反馈收集功能
    - 用curl命令或浏览器测试推荐API，确保返回的课程数据结构正确
    - 记录推荐接口的详细文档和数据格式
    - _需求: 4.1, 4.2, 4.3, 4.4, 4.5, 4.6_

- [-] 7. 讨论服务核心功能
  - [ ] 7.1 讨论服务基础框架
    - 创建discussion-service微服务项目
    - 实现讨论主题和回复实体类
    - 创建讨论相关的Repository接口
    - 配置与课程服务的集成
    - _需求: 5.1, 5.2_

  - [x] 7.2 讨论功能实现
    - 实现讨论主题创建和列表查询API
    - 实现讨论回复和嵌套回复功能
    - 实现点赞和热度排序功能
    - 实现讨论搜索和筛选功能
    - 用curl命令或浏览器测试讨论API，验证嵌套数据结构正确性
    - 详细记录讨论接口文档，特别是嵌套回复的数据格式
    - _需求: 5.1, 5.2, 5.3, 5.4, 5.5, 5.6_

- [-] 8. 前端Vue.js应用开发
  - [ ] 8.1 前端项目初始化
    - 创建Vue.js项目（选择Vue3）
    - 配置Vue Router路由管理
    - 配置Vuex状态管理
    - 配置Axios HTTP客户端
    - 创建基础的布局组件
    - _需求: 6.2_

  - [x] 8.2 用户认证页面
    - 严格按照用户服务API文档实现前端接口调用
    - 实现用户注册页面和表单验证，确保请求参数格式正确
    - 实现用户登录页面和JWT存储，验证返回数据处理
    - 实现个人资料页面和信息修改，确保数据绑定正确
    - 实现路由守卫和权限控制
    - 每个页面完成后立即测试前后端数据交互
    - _需求: 1.1, 1.2, 1.3, 1.4, 1.5_

  - [ ] 8.3 课程相关页面
    - 实现课程列表页面和搜索功能
    - 实现课程详情页面和报名功能
    - 实现教师课程管理页面
    - 实现课程创建和编辑页面
    - 测试课程相关功能
    - _需求: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

  - [ ] 8.4 学习相关页面
    - 实现学习仪表板页面
    - 实现课程学习页面和进度跟踪
    - 实现测验页面和成绩显示
    - 实现学习统计和成就展示
    - 测试学习功能流程
    - _需求: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6_

  - [ ] 8.5 推荐和讨论页面
    - 实现课程推荐页面和反馈功能
    - 实现讨论区页面和主题列表
    - 实现讨论详情页面和回复功能
    - 实现讨论搜索和筛选功能
    - 测试推荐和讨论功能
    - _需求: 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 5.1, 5.2, 5.3, 5.4, 5.5, 5.6_

- [ ] 9. 系统集成和测试
  - [ ] 9.1 微服务集成测试
    - 启动所有微服务并验证Nacos注册
    - 测试网关路由到各个微服务
    - 测试服务间调用和数据一致性
    - 验证数据库连接和数据持久化
    - _需求: 6.1, 6.4, 6.5_

  - [ ] 9.2 前后端联调测试
    - 先用curl命令或浏览器测试所有后端API，确保返回数据格式正确
    - 在前端代码中严格按照后端API文档编写接口调用
    - 测试前端登录和用户管理功能，验证JWT令牌处理
    - 测试课程创建、查看、报名流程，确保数据传递正确
    - 测试学习进度跟踪和统计功能，验证数据格式一致性
    - 测试推荐系统和讨论功能，确保前后端数据结构匹配
    - 每发现API不一致问题立即修复，不积累问题
    - _需求: 1.1-1.6, 2.1-2.6, 3.1-3.6, 4.1-4.6, 5.1-5.6_

- [ ] 10. 系统部署和优化
  - [ ] 10.1 本地部署配置
    - 配置MySQL数据库连接参数
    - 配置各微服务的端口和Nacos地址
    - 创建启动脚本和部署文档
    - 验证完整系统的本地部署
    - _需求: 6.3, 6.6_

  - [ ] 10.2 系统优化和文档
    - 优化数据库查询性能
    - 完善错误处理和用户提示
    - 编写系统使用说明文档
    - 进行最终的功能验收测试
    - _需求: 6.6_