package com.learningplatform.learning.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.Map;

/**
 * 课程服务客户端
 */
@FeignClient(name = "course-service")
public interface CourseServiceClient {
    
    /**
     * 获取课程信息
     */
    @GetMapping("/api/course/{id}")
    Map<String, Object> getCourseById(@PathVariable("id") Long id);
    
    /**
     * 获取课程章节列表
     */
    @GetMapping("/api/course/{courseId}/chapters")
    Map<String, Object> getCourseChapters(@PathVariable("courseId") Long courseId);
    
    /**
     * 获取章节信息
     */
    @GetMapping("/api/course/chapter/{chapterId}")
    Map<String, Object> getChapterById(@PathVariable("chapterId") Long chapterId);
}