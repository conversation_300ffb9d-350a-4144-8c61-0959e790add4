package com.learningplatform.learning.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 学习进度实体类
 */
@Entity
@Table(name = "learning_progress")
public class LearningProgress {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private Long userId;
    
    private Long courseId;
    
    private Long chapterId;
    
    private Boolean completed;
    
    private Integer watchDurationSeconds;
    
    private Integer lastPositionSeconds;
    
    private LocalDateTime completedAt;
    
    public LearningProgress() {}
    
    public LearningProgress(Long userId, Long courseId, Long chapterId) {
        this.userId = userId;
        this.courseId = courseId;
        this.chapterId = chapterId;
        this.completed = false;
        this.watchDurationSeconds = 0;
        this.lastPositionSeconds = 0;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Long getCourseId() {
        return courseId;
    }
    
    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }
    
    public Long getChapterId() {
        return chapterId;
    }
    
    public void setChapterId(Long chapterId) {
        this.chapterId = chapterId;
    }
    
    public Boolean getCompleted() {
        return completed;
    }
    
    public void setCompleted(Boolean completed) {
        this.completed = completed;
    }
    
    public Integer getWatchDurationSeconds() {
        return watchDurationSeconds;
    }
    
    public void setWatchDurationSeconds(Integer watchDurationSeconds) {
        this.watchDurationSeconds = watchDurationSeconds;
    }
    
    public Integer getLastPositionSeconds() {
        return lastPositionSeconds;
    }
    
    public void setLastPositionSeconds(Integer lastPositionSeconds) {
        this.lastPositionSeconds = lastPositionSeconds;
    }
    
    public LocalDateTime getCompletedAt() {
        return completedAt;
    }
    
    public void setCompletedAt(LocalDateTime completedAt) {
        this.completedAt = completedAt;
    }
}