package com.learningplatform.learning.controller;

import com.learningplatform.common.response.Result;
import com.learningplatform.learning.entity.CourseEnrollment;
import com.learningplatform.learning.entity.LearningProgress;
import com.learningplatform.learning.service.CourseEnrollmentService;
import com.learningplatform.learning.service.LearningProgressService;
import com.learningplatform.learning.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 学习进度控制器
 */
@RestController
@RequestMapping("/api/learning")
public class LearningController {
    
    @Autowired
    private CourseEnrollmentService courseEnrollmentService;

    @Autowired
    private LearningProgressService learningProgressService;

    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 用户注册课程
     */
    @PostMapping("/enroll")
    public Result<CourseEnrollment> enrollCourse(HttpServletRequest request, @RequestParam Long courseId) {
        try {
            Long userId = null;

            // 优先尝试从JWT token获取用户ID
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                try {
                    String token = authHeader.substring(7);
                    userId = jwtUtil.getUserIdFromToken(token);
                } catch (Exception e) {
                    // JWT解析失败，尝试从X-User-Id头获取
                    System.out.println("JWT解析失败，尝试从X-User-Id头获取用户ID");
                }
            }

            // 如果JWT获取失败，尝试从X-User-Id头获取
            if (userId == null) {
                String userIdHeader = request.getHeader("X-User-Id");
                if (userIdHeader != null) {
                    userId = Long.parseLong(userIdHeader);
                }
            }

            if (userId == null) {
                return Result.error("未提供有效的用户认证信息");
            }

            CourseEnrollment enrollment = courseEnrollmentService.enrollCourse(userId, courseId);
            return Result.success(enrollment);
        } catch (Exception e) {
            return Result.error("注册课程失败: " + e.getMessage());
        }
    }

    /**
     * 退选课程
     */
    @DeleteMapping("/unenroll/{courseId}")
    public Result<String> unenrollCourse(HttpServletRequest request, @PathVariable Long courseId) {
        try {
            Long userId = null;

            // 优先尝试从JWT token获取用户ID
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                try {
                    String token = authHeader.substring(7);
                    userId = jwtUtil.getUserIdFromToken(token);
                } catch (Exception e) {
                    // JWT解析失败，尝试从X-User-Id头获取
                    System.out.println("JWT解析失败，尝试从X-User-Id头获取用户ID");
                }
            }

            // 如果JWT获取失败，尝试从X-User-Id头获取
            if (userId == null) {
                String userIdHeader = request.getHeader("X-User-Id");
                if (userIdHeader != null) {
                    userId = Long.parseLong(userIdHeader);
                }
            }

            if (userId == null) {
                return Result.error("未提供有效的用户认证信息");
            }

            courseEnrollmentService.unenrollCourse(userId, courseId);
            return Result.success("退选课程成功");
        } catch (Exception e) {
            return Result.error("退选课程失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户已注册的课程列表
     */
    @GetMapping("/enrollments")
    public Result<List<CourseEnrollment>> getUserEnrollments(HttpServletRequest request) {
        try {
            // 从请求头中获取JWT token
            String authHeader = request.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return Result.error("未提供有效的认证令牌");
            }

            String token = authHeader.substring(7);
            Long userId = jwtUtil.getUserIdFromToken(token);

            List<CourseEnrollment> enrollments = courseEnrollmentService.getUserEnrollments(userId);
            return Result.success(enrollments);
        } catch (Exception e) {
            return Result.error("获取注册课程失败: " + e.getMessage());
        }
    }

    /**
     * 获取已注册课程（前端兼容性路径）
     */
    @GetMapping("/enrolled-courses")
    public Result<List<CourseEnrollment>> getEnrolledCourses(HttpServletRequest request) {
        return getUserEnrollments(request);
    }

    /**
     * 检查用户是否已注册某课程
     */
    @GetMapping("/enrolled")
    public Result<Boolean> isEnrolled(HttpServletRequest request, @RequestParam Long courseId) {
        try {
            // 从请求头中获取JWT token
            String authHeader = request.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return Result.error("未提供有效的认证令牌");
            }

            String token = authHeader.substring(7);
            Long userId = jwtUtil.getUserIdFromToken(token);

            boolean enrolled = courseEnrollmentService.isEnrolled(userId, courseId);
            return Result.success(enrolled);
        } catch (Exception e) {
            return Result.error("检查注册状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取学习进度
     */
    @GetMapping("/progress/{courseId}")
    public Result<List<LearningProgress>> getCourseProgress(@PathVariable Long courseId, HttpServletRequest request) {
        try {
            Long userId = null;

            // 优先尝试从JWT token获取用户ID
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                try {
                    String token = authHeader.substring(7);
                    userId = jwtUtil.getUserIdFromToken(token);
                } catch (Exception e) {
                    // JWT解析失败，尝试从X-User-Id头获取
                    System.out.println("JWT解析失败，尝试从X-User-Id头获取用户ID");
                }
            }

            // 如果JWT获取失败，尝试从X-User-Id头获取
            if (userId == null) {
                String userIdHeader = request.getHeader("X-User-Id");
                if (userIdHeader != null) {
                    userId = Long.parseLong(userIdHeader);
                }
            }

            if (userId == null) {
                return Result.error("未提供有效的用户认证信息");
            }

            List<LearningProgress> progress = learningProgressService.getCourseProgress(userId, courseId);
            return Result.success(progress);
        } catch (Exception e) {
            return Result.error("获取学习进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新学习进度
     */
    @PostMapping("/progress")
    public Result<LearningProgress> updateProgress(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            Long courseId = Long.valueOf(request.get("courseId").toString());
            Long chapterId = Long.valueOf(request.get("chapterId").toString());
            Integer watchDurationSeconds = Integer.valueOf(request.get("watchDurationSeconds").toString());
            Integer lastPositionSeconds = Integer.valueOf(request.get("lastPositionSeconds").toString());
            
            LearningProgress progress = learningProgressService.updateProgress(
                userId, courseId, chapterId, watchDurationSeconds, lastPositionSeconds);
            return Result.success(progress);
        } catch (Exception e) {
            return Result.error("更新学习进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 完成章节学习
     */
    @PostMapping("/progress/complete")
    public Result<String> completeChapter(HttpServletRequest httpRequest, @RequestBody Map<String, Object> request) {
        try {
            Long userId = null;
            Long courseId = Long.valueOf(request.get("courseId").toString());
            Long chapterId = Long.valueOf(request.get("chapterId").toString());

            // 优先从请求体获取userId
            if (request.containsKey("userId")) {
                userId = Long.valueOf(request.get("userId").toString());
            } else {
                // 尝试从JWT token获取用户ID
                String authHeader = httpRequest.getHeader("Authorization");
                if (authHeader != null && authHeader.startsWith("Bearer ")) {
                    try {
                        String token = authHeader.substring(7);
                        userId = jwtUtil.getUserIdFromToken(token);
                    } catch (Exception e) {
                        // JWT解析失败，尝试从X-User-Id头获取
                        System.out.println("JWT解析失败，尝试从X-User-Id头获取用户ID");
                    }
                }

                // 如果JWT获取失败，尝试从X-User-Id头获取
                if (userId == null) {
                    String userIdHeader = httpRequest.getHeader("X-User-Id");
                    if (userIdHeader != null) {
                        userId = Long.parseLong(userIdHeader);
                    }
                }
            }

            if (userId == null) {
                return Result.error("未提供有效的用户认证信息");
            }

            learningProgressService.completeChapter(userId, courseId, chapterId);
            return Result.success("章节完成成功");
        } catch (Exception e) {
            return Result.error("完成章节失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取学习仪表板数据
     */
    @GetMapping("/dashboard")
    public Result<Map<String, Object>> getLearningDashboard(HttpServletRequest request) {
        try {
            Long userId = null;

            // 优先尝试从JWT token获取用户ID
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                try {
                    String token = authHeader.substring(7);
                    userId = jwtUtil.getUserIdFromToken(token);
                } catch (Exception e) {
                    // JWT解析失败，尝试从X-User-Id头获取
                    System.out.println("JWT解析失败，尝试从X-User-Id头获取用户ID");
                }
            }

            // 如果JWT获取失败，尝试从X-User-Id头获取
            if (userId == null) {
                String userIdHeader = request.getHeader("X-User-Id");
                if (userIdHeader != null) {
                    userId = Long.parseLong(userIdHeader);
                }
            }

            if (userId == null) {
                return Result.error("未提供有效的用户认证信息");
            }

            Map<String, Object> dashboard = learningProgressService.getLearningDashboard(userId);
            return Result.success(dashboard);
        } catch (Exception e) {
            return Result.error("获取学习仪表板失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取学习统计数据
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getLearningStatistics(HttpServletRequest request) {
        try {
            // 从请求头中获取JWT token
            String authHeader = request.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return Result.error("未提供有效的认证令牌");
            }

            String token = authHeader.substring(7);
            Long userId = jwtUtil.getUserIdFromToken(token);

            Map<String, Object> statistics = learningProgressService.getLearningStatistics(userId);
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error("获取学习统计失败: " + e.getMessage());
        }
    }
}