package com.learningplatform.learning.controller;

import com.learningplatform.common.response.Result;
import com.learningplatform.learning.entity.QuizRecord;
import com.learningplatform.learning.service.QuizService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 测验控制器
 */
@RestController
@RequestMapping("/api/learning/quiz")
public class QuizController {
    
    @Autowired
    private QuizService quizService;
    
    /**
     * 提交测验
     */
    @PostMapping("/submit")
    public Result<QuizRecord> submitQuiz(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            Long chapterId = Long.valueOf(request.get("chapterId").toString());
            BigDecimal score = new BigDecimal(request.get("score").toString());
            BigDecimal totalScore = new BigDecimal(request.get("totalScore").toString());
            
            QuizRecord record = quizService.submitQuiz(userId, chapterId, score, totalScore);
            return Result.success(record);
        } catch (Exception e) {
            return Result.error("提交测验失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户的测验记录
     */
    @GetMapping("/records")
    public Result<List<QuizRecord>> getUserQuizRecords(@RequestParam Long userId) {
        List<QuizRecord> records = quizService.getUserQuizRecords(userId);
        return Result.success(records);
    }
    
    /**
     * 获取用户在某章节的测验记录
     */
    @GetMapping("/records/{chapterId}")
    public Result<List<QuizRecord>> getChapterQuizRecords(@PathVariable Long chapterId, @RequestParam Long userId) {
        List<QuizRecord> records = quizService.getChapterQuizRecords(userId, chapterId);
        return Result.success(records);
    }
    
    /**
     * 获取用户在某章节的最高分
     */
    @GetMapping("/max-score/{chapterId}")
    public Result<Double> getMaxScore(@PathVariable Long chapterId, @RequestParam Long userId) {
        Double maxScore = quizService.getMaxScore(userId, chapterId);
        return Result.success(maxScore);
    }
    
    /**
     * 获取测验统计数据
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getQuizStatistics(@RequestParam Long userId) {
        Map<String, Object> statistics = quizService.getQuizStatistics(userId);
        return Result.success(statistics);
    }
}