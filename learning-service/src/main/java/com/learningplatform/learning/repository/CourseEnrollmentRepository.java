package com.learningplatform.learning.repository;

import com.learningplatform.learning.entity.CourseEnrollment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 课程注册数据访问接口
 */
@Repository
public interface CourseEnrollmentRepository extends JpaRepository<CourseEnrollment, Long> {
    
    /**
     * 根据用户ID查询已注册的课程
     */
    List<CourseEnrollment> findByUserId(Long userId);
    
    /**
     * 根据课程ID查询注册用户
     */
    List<CourseEnrollment> findByCourseId(Long courseId);
    
    /**
     * 查询用户是否已注册某课程
     */
    Optional<CourseEnrollment> findByUserIdAndCourseId(Long userId, Long courseId);
}