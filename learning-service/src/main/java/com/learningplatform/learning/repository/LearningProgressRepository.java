package com.learningplatform.learning.repository;

import com.learningplatform.learning.entity.LearningProgress;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 学习进度数据访问接口
 */
@Repository
public interface LearningProgressRepository extends JpaRepository<LearningProgress, Long> {
    
    /**
     * 根据用户ID和课程ID查询学习进度
     */
    List<LearningProgress> findByUserIdAndCourseId(Long userId, Long courseId);
    
    /**
     * 根据用户ID、课程ID和章节ID查询学习进度
     */
    Optional<LearningProgress> findByUserIdAndCourseIdAndChapterId(Long userId, Long courseId, Long chapterId);
    
    /**
     * 根据用户ID查询所有学习进度
     */
    List<LearningProgress> findByUserId(Long userId);
    
    /**
     * 统计用户在某课程中已完成的章节数
     */
    @Query("SELECT COUNT(lp) FROM LearningProgress lp WHERE lp.userId = :userId AND lp.courseId = :courseId AND lp.completed = true")
    Integer countCompletedChaptersByUserIdAndCourseId(@Param("userId") Long userId, @Param("courseId") Long courseId);
}