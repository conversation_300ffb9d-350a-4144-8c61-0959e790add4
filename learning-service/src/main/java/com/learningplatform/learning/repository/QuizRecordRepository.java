package com.learningplatform.learning.repository;

import com.learningplatform.learning.entity.QuizRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 测验记录数据访问接口
 */
@Repository
public interface QuizRecordRepository extends JpaRepository<QuizRecord, Long> {
    
    /**
     * 根据用户ID查询测验记录
     */
    List<QuizRecord> findByUserIdOrderBySubmittedAtDesc(Long userId);
    
    /**
     * 根据用户ID和章节ID查询测验记录
     */
    List<QuizRecord> findByUserIdAndChapterIdOrderBySubmittedAtDesc(Long userId, Long chapterId);
    
    /**
     * 获取用户在某章节的最高分数
     */
    @Query("SELECT MAX(qr.score) FROM QuizRecord qr WHERE qr.userId = :userId AND qr.chapterId = :chapterId")
    Double getMaxScoreByUserIdAndChapterId(@Param("userId") Long userId, @Param("chapterId") Long chapterId);
}