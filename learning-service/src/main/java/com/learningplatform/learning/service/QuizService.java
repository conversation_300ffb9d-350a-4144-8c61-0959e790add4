package com.learningplatform.learning.service;

import com.learningplatform.learning.entity.QuizRecord;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 测验服务接口
 */
public interface QuizService {
    
    /**
     * 提交测验
     */
    QuizRecord submitQuiz(Long userId, Long chapterId, BigDecimal score, BigDecimal totalScore);
    
    /**
     * 获取用户的测验记录
     */
    List<QuizRecord> getUserQuizRecords(Long userId);
    
    /**
     * 获取用户在某章节的测验记录
     */
    List<QuizRecord> getChapterQuizRecords(Long userId, Long chapterId);
    
    /**
     * 获取用户在某章节的最高分
     */
    Double getMaxScore(Long userId, Long chapterId);
    
    /**
     * 获取测验统计数据
     */
    Map<String, Object> getQuizStatistics(Long userId);
}