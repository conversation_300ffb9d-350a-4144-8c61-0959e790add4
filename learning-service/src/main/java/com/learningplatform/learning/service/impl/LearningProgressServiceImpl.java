package com.learningplatform.learning.service.impl;

import com.learningplatform.learning.client.CourseServiceClient;
import com.learningplatform.learning.entity.CourseEnrollment;
import com.learningplatform.learning.entity.LearningProgress;
import com.learningplatform.learning.repository.CourseEnrollmentRepository;
import com.learningplatform.learning.repository.LearningProgressRepository;
import com.learningplatform.learning.service.CourseEnrollmentService;
import com.learningplatform.learning.service.LearningProgressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 学习进度服务实现类
 */
@Service
public class LearningProgressServiceImpl implements LearningProgressService {
    
    @Autowired
    private LearningProgressRepository learningProgressRepository;
    
    @Autowired
    private CourseEnrollmentRepository courseEnrollmentRepository;
    
    @Autowired
    private CourseEnrollmentService courseEnrollmentService;
    
    @Autowired
    private CourseServiceClient courseServiceClient;
    
    @Override
    public LearningProgress updateProgress(Long userId, Long courseId, Long chapterId, 
                                         Integer watchDurationSeconds, Integer lastPositionSeconds) {
        // 检查是否已有进度记录
        Optional<LearningProgress> progressOpt = learningProgressRepository.findByUserIdAndCourseIdAndChapterId(userId, courseId, chapterId);
        
        LearningProgress progress;
        if (progressOpt.isEmpty()) {
            // 创建新的进度记录
            progress = new LearningProgress(userId, courseId, chapterId);
        } else {
            progress = progressOpt.get();
        }
        
        // 更新进度信息
        progress.setWatchDurationSeconds(watchDurationSeconds);
        progress.setLastPositionSeconds(lastPositionSeconds);
        
        progress = learningProgressRepository.save(progress);
        
        // 更新课程整体进度
        Double courseProgress = calculateCourseProgress(userId, courseId);
        courseEnrollmentService.updateProgress(userId, courseId, courseProgress);
        
        return progress;
    }
    
    @Override
    public void completeChapter(Long userId, Long courseId, Long chapterId) {
        Optional<LearningProgress> progressOpt = learningProgressRepository.findByUserIdAndCourseIdAndChapterId(userId, courseId, chapterId);
        
        LearningProgress progress;
        if (progressOpt.isEmpty()) {
            progress = new LearningProgress(userId, courseId, chapterId);
        } else {
            progress = progressOpt.get();
        }
        
        progress.setCompleted(true);
        progress.setCompletedAt(LocalDateTime.now());
        
        learningProgressRepository.save(progress);
        
        // 更新课程整体进度
        Double courseProgress = calculateCourseProgress(userId, courseId);
        courseEnrollmentService.updateProgress(userId, courseId, courseProgress);
        
        // 检查是否完成整个课程
        if (courseProgress >= 100.0) {
            courseEnrollmentService.completeCourse(userId, courseId);
        }
    }
    
    @Override
    public List<LearningProgress> getCourseProgress(Long userId, Long courseId) {
        return learningProgressRepository.findByUserIdAndCourseId(userId, courseId);
    }
    
    @Override
    public Map<String, Object> getLearningDashboard(Long userId) {
        Map<String, Object> dashboard = new HashMap<>();

        // 获取用户注册的课程
        List<CourseEnrollment> enrollments = courseEnrollmentRepository.findByUserId(userId);
        dashboard.put("totalEnrolledCourses", enrollments.size());

        // 计算完成的课程数
        long completedCourses = enrollments.stream()
                .filter(e -> e.getCompletedAt() != null)
                .count();
        dashboard.put("completedCourses", completedCourses);

        // 计算正在学习的课程数
        long inProgressCourses = enrollments.size() - completedCourses;
        dashboard.put("inProgressCourses", inProgressCourses);

        // 获取所有学习进度
        List<LearningProgress> allProgress = learningProgressRepository.findByUserId(userId);

        // 计算总学习时长（小时）
        int totalWatchSeconds = allProgress.stream()
                .mapToInt(p -> p.getWatchDurationSeconds() != null ? p.getWatchDurationSeconds() : 0)
                .sum();
        double totalHours = totalWatchSeconds / 3600.0;
        dashboard.put("totalLearningHours", Math.round(totalHours * 10) / 10.0);

        // 计算完成的章节数
        long completedChapters = allProgress.stream()
                .filter(p -> Boolean.TRUE.equals(p.getCompleted()))
                .count();
        dashboard.put("completedChapters", completedChapters);

        // 最近的学习记录 - 包含课程详细信息
        List<Map<String, Object>> recentEnrollmentsWithDetails = enrollments.stream()
                .sorted((a, b) -> b.getEnrolledAt().compareTo(a.getEnrolledAt()))
                .limit(5)
                .map(enrollment -> {
                    Map<String, Object> enrollmentData = new HashMap<>();
                    enrollmentData.put("id", enrollment.getId());
                    enrollmentData.put("userId", enrollment.getUserId());
                    enrollmentData.put("courseId", enrollment.getCourseId());
                    enrollmentData.put("enrolledAt", enrollment.getEnrolledAt());
                    enrollmentData.put("completedAt", enrollment.getCompletedAt());
                    enrollmentData.put("progressPercentage", enrollment.getProgressPercentage());

                    // 获取课程详细信息
                    try {
                        Map<String, Object> courseResponse = courseServiceClient.getCourseById(enrollment.getCourseId());
                        if (courseResponse != null && Boolean.TRUE.equals(courseResponse.get("success"))) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> courseData = (Map<String, Object>) courseResponse.get("data");
                            if (courseData != null) {
                                enrollmentData.put("courseTitle", courseData.get("title"));
                                enrollmentData.put("courseCoverImage", courseData.get("coverImage"));
                                enrollmentData.put("courseDescription", courseData.get("description"));
                                enrollmentData.put("teacherName", courseData.get("teacherName"));
                                enrollmentData.put("difficultyLevel", courseData.get("difficultyLevel"));
                            }
                        }
                    } catch (Exception e) {
                        // 如果获取课程信息失败，使用默认值
                        enrollmentData.put("courseTitle", "课程 #" + enrollment.getCourseId());
                        enrollmentData.put("courseCoverImage", null);
                        enrollmentData.put("courseDescription", "");
                        enrollmentData.put("teacherName", "");
                        enrollmentData.put("difficultyLevel", "");
                    }

                    return enrollmentData;
                })
                .toList();

        dashboard.put("recentEnrollments", recentEnrollmentsWithDetails);

        return dashboard;
    }
    
    @Override
    public Map<String, Object> getLearningStatistics(Long userId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取基础统计数据
        List<CourseEnrollment> enrollments = courseEnrollmentRepository.findByUserId(userId);
        List<LearningProgress> allProgress = learningProgressRepository.findByUserId(userId);
        
        // 按月统计学习进度
        Map<String, Integer> monthlyProgress = new HashMap<>();
        // 这里可以添加更复杂的统计逻辑
        
        statistics.put("totalCourses", enrollments.size());
        statistics.put("totalChapters", allProgress.size());
        statistics.put("completedChapters", allProgress.stream()
                .filter(p -> Boolean.TRUE.equals(p.getCompleted()))
                .count());
        
        // 平均进度
        double avgProgress = enrollments.stream()
                .mapToDouble(e -> e.getProgressPercentage().doubleValue())
                .average()
                .orElse(0.0);
        statistics.put("averageProgress", Math.round(avgProgress * 10) / 10.0);
        
        return statistics;
    }
    
    @Override
    public Double calculateCourseProgress(Long userId, Long courseId) {
        try {
            // 获取课程章节信息
            Map<String, Object> chaptersResponse = courseServiceClient.getCourseChapters(courseId);
            
            if (chaptersResponse == null || !Boolean.TRUE.equals(chaptersResponse.get("success"))) {
                return 0.0;
            }
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> chapters = (List<Map<String, Object>>) chaptersResponse.get("data");
            
            if (chapters == null || chapters.isEmpty()) {
                return 0.0;
            }
            
            // 获取用户已完成的章节数
            Integer completedCount = learningProgressRepository.countCompletedChaptersByUserIdAndCourseId(userId, courseId);
            
            // 计算进度百分比
            double progress = (completedCount.doubleValue() / chapters.size()) * 100.0;
            return Math.min(progress, 100.0);
            
        } catch (Exception e) {
            // 如果调用课程服务失败，返回基于现有数据的估算
            List<LearningProgress> userProgress = learningProgressRepository.findByUserIdAndCourseId(userId, courseId);
            if (userProgress.isEmpty()) {
                return 0.0;
            }
            
            long completedCount = userProgress.stream()
                    .filter(p -> Boolean.TRUE.equals(p.getCompleted()))
                    .count();
            
            return (completedCount * 100.0) / userProgress.size();
        }
    }
}