package com.learningplatform.learning.service;

import com.learningplatform.learning.entity.LearningProgress;
import java.util.List;
import java.util.Map;

/**
 * 学习进度服务接口
 */
public interface LearningProgressService {
    
    /**
     * 更新学习进度
     */
    LearningProgress updateProgress(Long userId, Long courseId, Long chapterId, 
                                  Integer watchDurationSeconds, Integer lastPositionSeconds);
    
    /**
     * 完成章节学习
     */
    void completeChapter(Long userId, Long courseId, Long chapterId);
    
    /**
     * 获取用户在某课程的学习进度
     */
    List<LearningProgress> getCourseProgress(Long userId, Long courseId);
    
    /**
     * 获取用户的学习仪表板数据
     */
    Map<String, Object> getLearningDashboard(Long userId);
    
    /**
     * 获取用户学习统计数据
     */
    Map<String, Object> getLearningStatistics(Long userId);
    
    /**
     * 计算课程整体进度
     */
    Double calculateCourseProgress(Long userId, Long courseId);
}