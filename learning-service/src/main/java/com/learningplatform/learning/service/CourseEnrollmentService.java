package com.learningplatform.learning.service;

import com.learningplatform.learning.entity.CourseEnrollment;
import java.util.List;

/**
 * 课程注册服务接口
 */
public interface CourseEnrollmentService {
    
    /**
     * 用户注册课程
     */
    CourseEnrollment enrollCourse(Long userId, Long courseId);
    
    /**
     * 获取用户已注册的课程列表
     */
    List<CourseEnrollment> getUserEnrollments(Long userId);
    
    /**
     * 检查用户是否已注册某课程
     */
    boolean isEnrolled(Long userId, Long courseId);
    
    /**
     * 获取用户在某课程的注册信息
     */
    CourseEnrollment getEnrollment(Long userId, Long courseId);
    
    /**
     * 更新课程学习进度
     */
    void updateProgress(Long userId, Long courseId, Double progressPercentage);
    
    /**
     * 完成课程学习
     */
    void completeCourse(Long userId, Long courseId);

    /**
     * 退选课程
     */
    void unenrollCourse(Long userId, Long courseId);
}