package com.learningplatform.learning.service.impl;

import com.learningplatform.learning.entity.QuizRecord;
import com.learningplatform.learning.repository.QuizRecordRepository;
import com.learningplatform.learning.service.QuizService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测验服务实现类
 */
@Service
public class QuizServiceImpl implements QuizService {
    
    @Autowired
    private QuizRecordRepository quizRecordRepository;
    
    @Override
    public QuizRecord submitQuiz(Long userId, Long chapterId, BigDecimal score, BigDecimal totalScore) {
        QuizRecord record = new QuizRecord(userId, chapterId, score, totalScore);
        return quizRecordRepository.save(record);
    }
    
    @Override
    public List<QuizRecord> getUserQuizRecords(Long userId) {
        return quizRecordRepository.findByUserIdOrderBySubmittedAtDesc(userId);
    }
    
    @Override
    public List<QuizRecord> getChapterQuizRecords(Long userId, Long chapterId) {
        return quizRecordRepository.findByUserIdAndChapterIdOrderBySubmittedAtDesc(userId, chapterId);
    }
    
    @Override
    public Double getMaxScore(Long userId, Long chapterId) {
        Double maxScore = quizRecordRepository.getMaxScoreByUserIdAndChapterId(userId, chapterId);
        return maxScore != null ? maxScore : 0.0;
    }
    
    @Override
    public Map<String, Object> getQuizStatistics(Long userId) {
        Map<String, Object> statistics = new HashMap<>();
        
        List<QuizRecord> records = quizRecordRepository.findByUserIdOrderBySubmittedAtDesc(userId);
        
        statistics.put("totalQuizzes", records.size());
        
        if (!records.isEmpty()) {
            // 计算平均分
            double avgScore = records.stream()
                    .mapToDouble(r -> r.getScore().doubleValue())
                    .average()
                    .orElse(0.0);
            statistics.put("averageScore", Math.round(avgScore * 100) / 100.0);
            
            // 计算最高分
            double maxScore = records.stream()
                    .mapToDouble(r -> r.getScore().doubleValue())
                    .max()
                    .orElse(0.0);
            statistics.put("maxScore", maxScore);
            
            // 计算通过率（假设60分及格）
            long passedCount = records.stream()
                    .filter(r -> {
                        double percentage = (r.getScore().doubleValue() / r.getTotalScore().doubleValue()) * 100;
                        return percentage >= 60.0;
                    })
                    .count();
            double passRate = (passedCount * 100.0) / records.size();
            statistics.put("passRate", Math.round(passRate * 10) / 10.0);
        } else {
            statistics.put("averageScore", 0.0);
            statistics.put("maxScore", 0.0);
            statistics.put("passRate", 0.0);
        }
        
        return statistics;
    }
}