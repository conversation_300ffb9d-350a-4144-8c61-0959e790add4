package com.learningplatform.learning.service.impl;

import com.learningplatform.learning.entity.CourseEnrollment;
import com.learningplatform.learning.repository.CourseEnrollmentRepository;
import com.learningplatform.learning.service.CourseEnrollmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 课程注册服务实现类
 */
@Service
public class CourseEnrollmentServiceImpl implements CourseEnrollmentService {
    
    @Autowired
    private CourseEnrollmentRepository courseEnrollmentRepository;
    
    @Override
    public CourseEnrollment enrollCourse(Long userId, Long courseId) {
        // 检查是否已经注册
        Optional<CourseEnrollment> existing = courseEnrollmentRepository.findByUserIdAndCourseId(userId, courseId);
        if (existing.isPresent()) {
            throw new RuntimeException("用户已经注册了该课程");
        }
        
        // 创建新的注册记录
        CourseEnrollment enrollment = new CourseEnrollment(userId, courseId);
        return courseEnrollmentRepository.save(enrollment);
    }
    
    @Override
    public List<CourseEnrollment> getUserEnrollments(Long userId) {
        return courseEnrollmentRepository.findByUserId(userId);
    }
    
    @Override
    public boolean isEnrolled(Long userId, Long courseId) {
        return courseEnrollmentRepository.findByUserIdAndCourseId(userId, courseId).isPresent();
    }
    
    @Override
    public CourseEnrollment getEnrollment(Long userId, Long courseId) {
        return courseEnrollmentRepository.findByUserIdAndCourseId(userId, courseId).orElse(null);
    }
    
    @Override
    public void updateProgress(Long userId, Long courseId, Double progressPercentage) {
        Optional<CourseEnrollment> enrollmentOpt = courseEnrollmentRepository.findByUserIdAndCourseId(userId, courseId);
        if (enrollmentOpt.isEmpty()) {
            throw new RuntimeException("用户未注册该课程");
        }
        
        CourseEnrollment enrollment = enrollmentOpt.get();
        enrollment.setProgressPercentage(BigDecimal.valueOf(progressPercentage));
        courseEnrollmentRepository.save(enrollment);
    }
    
    @Override
    public void completeCourse(Long userId, Long courseId) {
        Optional<CourseEnrollment> enrollmentOpt = courseEnrollmentRepository.findByUserIdAndCourseId(userId, courseId);
        if (enrollmentOpt.isEmpty()) {
            throw new RuntimeException("用户未注册该课程");
        }
        
        CourseEnrollment enrollment = enrollmentOpt.get();
        enrollment.setProgressPercentage(BigDecimal.valueOf(100.0));
        enrollment.setCompletedAt(LocalDateTime.now());
        courseEnrollmentRepository.save(enrollment);
    }

    @Override
    public void unenrollCourse(Long userId, Long courseId) {
        Optional<CourseEnrollment> enrollmentOpt = courseEnrollmentRepository.findByUserIdAndCourseId(userId, courseId);
        if (enrollmentOpt.isEmpty()) {
            throw new RuntimeException("用户未注册该课程，无法退选");
        }

        CourseEnrollment enrollment = enrollmentOpt.get();
        courseEnrollmentRepository.delete(enrollment);
    }
}