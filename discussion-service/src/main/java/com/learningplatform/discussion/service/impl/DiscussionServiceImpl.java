package com.learningplatform.discussion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.learningplatform.discussion.dto.*;
import com.learningplatform.discussion.entity.DiscussionReply;
import com.learningplatform.discussion.entity.DiscussionTopic;
// import com.learningplatform.discussion.feign.CourseServiceClient;
// import com.learningplatform.discussion.feign.UserServiceClient;
import com.learningplatform.discussion.mapper.DiscussionReplyMapper;
import com.learningplatform.discussion.mapper.DiscussionTopicMapper;
import com.learningplatform.discussion.mapper.ReplyLikeMapper;
import com.learningplatform.discussion.mapper.TopicLikeMapper;
import com.learningplatform.discussion.entity.ReplyLike;
import com.learningplatform.discussion.entity.TopicLike;
import com.learningplatform.discussion.feign.UserServiceClient;
import com.learningplatform.common.response.Result;
import com.learningplatform.discussion.service.DiscussionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 讨论服务实现类
 */
@Service
public class DiscussionServiceImpl implements DiscussionService {
    
    private static final Logger log = LoggerFactory.getLogger(DiscussionServiceImpl.class);
    
    private final DiscussionTopicMapper topicMapper;
    private final DiscussionReplyMapper replyMapper;
    private final ReplyLikeMapper replyLikeMapper;
    private final TopicLikeMapper topicLikeMapper;
    private final UserServiceClient userServiceClient;

    public DiscussionServiceImpl(DiscussionTopicMapper topicMapper,
                                DiscussionReplyMapper replyMapper,
                                ReplyLikeMapper replyLikeMapper,
                                TopicLikeMapper topicLikeMapper,
                                UserServiceClient userServiceClient) {
        this.topicMapper = topicMapper;
        this.replyMapper = replyMapper;
        this.replyLikeMapper = replyLikeMapper;
        this.topicLikeMapper = topicLikeMapper;
        this.userServiceClient = userServiceClient;
    }
    
    @Override
    @Transactional
    public DiscussionTopicResponse createTopic(Long userId, DiscussionTopicCreateRequest request) {
        log.info("创建讨论主题，用户ID: {}, 课程ID: {}", userId, request.getCourseId());
        
        // 验证课程是否存在 - 暂时跳过，后续集成时再实现
        // try {
        //     Boolean courseExists = courseServiceClient.checkCourseExists(request.getCourseId());
        //     if (!Boolean.TRUE.equals(courseExists)) {
        //         throw new RuntimeException("课程不存在");
        //     }
        // } catch (Exception e) {
        //     log.warn("调用课程服务失败，跳过课程验证: {}", e.getMessage());
        // }
        
        // 创建讨论主题
        DiscussionTopic topic = new DiscussionTopic();
        topic.setCourseId(request.getCourseId());
        topic.setUserId(userId);
        topic.setTitle(request.getTitle());
        topic.setContent(request.getContent());
        topic.setReplyCount(0);
        topic.setViewCount(0);
        topic.setIsPinned(false);
        topic.setStatus("ACTIVE");
        topic.setCreatedAt(java.time.LocalDateTime.now());
        topic.setUpdatedAt(java.time.LocalDateTime.now());
        
        topicMapper.insert(topic);
        
        return convertToTopicResponse(topic);
    }
    
    @Override
    public IPage<DiscussionTopicResponse> getTopicsByCourseId(Long courseId, Integer page, Integer size, String sortBy) {
        log.info("获取课程讨论主题列表，课程ID: {}, 页码: {}, 大小: {}, 排序: {}", courseId, page, size, sortBy);
        
        Page<DiscussionTopic> pageParam = new Page<>(page, size);
        IPage<DiscussionTopic> topicPage;
        
        switch (sortBy) {
            case "hot":
                topicPage = topicMapper.selectTopicsByCourseIdOrderByHot(pageParam, courseId);
                break;
            case "replies":
                topicPage = topicMapper.selectTopicsByCourseIdOrderByReplies(pageParam, courseId);
                break;
            case "views":
                topicPage = topicMapper.selectTopicsByCourseIdOrderByViews(pageParam, courseId);
                break;
            case "latest":
            default:
                topicPage = topicMapper.selectTopicsByCourseId(pageParam, courseId);
                break;
        }
        
        return topicPage.convert(this::convertToTopicResponse);
    }
    
    @Override
    public IPage<DiscussionTopicResponse> getHotTopics(Long courseId, Integer page, Integer size) {
        log.info("获取热门讨论主题列表，课程ID: {}, 页码: {}, 大小: {}", courseId, page, size);
        
        Page<DiscussionTopic> pageParam = new Page<>(page, size);
        IPage<DiscussionTopic> topicPage = topicMapper.selectHotTopics(pageParam, courseId);
        
        return topicPage.convert(this::convertToTopicResponse);
    }
    
    @Override
    public DiscussionTopicResponse getTopicById(Long topicId) {
        log.info("获取讨论主题详情，主题ID: {}", topicId);
        
        DiscussionTopic topic = topicMapper.selectById(topicId);
        if (topic == null) {
            throw new RuntimeException("讨论主题不存在");
        }
        
        // 增加查看次数
        topicMapper.incrementViewCount(topicId);
        topic.setViewCount(topic.getViewCount() + 1);
        
        return convertToTopicResponse(topic);
    }
    
    @Override
    public IPage<DiscussionTopicResponse> searchTopics(Long courseId, String keyword, Integer page, Integer size) {
        log.info("搜索讨论主题，课程ID: {}, 关键词: {}", courseId, keyword);
        
        Page<DiscussionTopic> pageParam = new Page<>(page, size);
        IPage<DiscussionTopic> topicPage = topicMapper.searchTopics(pageParam, courseId, keyword);
        
        return topicPage.convert(this::convertToTopicResponse);
    }
    
    @Override
    @Transactional
    public DiscussionReplyResponse createReply(Long userId, DiscussionReplyCreateRequest request) {
        log.info("创建讨论回复，用户ID: {}, 主题ID: {}", userId, request.getTopicId());
        
        // 验证主题是否存在
        DiscussionTopic topic = topicMapper.selectById(request.getTopicId());
        if (topic == null) {
            throw new RuntimeException("讨论主题不存在");
        }
        
        // 如果是嵌套回复，验证父回复是否存在
        if (request.getParentId() != null) {
            DiscussionReply parentReply = replyMapper.selectById(request.getParentId());
            if (parentReply == null) {
                throw new RuntimeException("父回复不存在");
            }
        }
        
        // 创建回复
        DiscussionReply reply = new DiscussionReply();
        reply.setTopicId(request.getTopicId());
        reply.setUserId(userId);
        reply.setParentId(request.getParentId());
        reply.setContent(request.getContent());
        reply.setLikeCount(0);
        reply.setStatus("ACTIVE");
        reply.setCreatedAt(java.time.LocalDateTime.now());
        reply.setUpdatedAt(java.time.LocalDateTime.now());
        
        replyMapper.insert(reply);
        
        // 更新主题回复数量
        topicMapper.incrementReplyCount(request.getTopicId());
        
        return convertToReplyResponse(reply);
    }
    
    @Override
    public IPage<DiscussionReplyResponse> getRepliesByTopicId(Long topicId, Integer page, Integer size, String sortBy) {
        log.info("获取主题回复列表，主题ID: {}, 页码: {}, 大小: {}, 排序: {}", topicId, page, size, sortBy);

        Page<DiscussionReply> pageParam = new Page<>(page, size);
        IPage<DiscussionReply> replyPage;

        switch (sortBy) {
            case "like_count":
                replyPage = replyMapper.selectRepliesByTopicIdOrderByLikes(pageParam, topicId);
                break;
            case "created_at":
            default:
                replyPage = replyMapper.selectRepliesByTopicId(pageParam, topicId);
                break;
        }

        return replyPage.convert(this::convertToReplyResponse);
    }
    
    @Override
    public List<DiscussionReplyResponse> getAllRepliesByTopicId(Long topicId) {
        log.info("获取主题所有回复（嵌套结构），主题ID: {}", topicId);
        
        List<DiscussionReply> allReplies = replyMapper.selectAllRepliesByTopicId(topicId);
        
        // 构建嵌套结构
        Map<Long, DiscussionReplyResponse> replyMap = new HashMap<>();
        List<DiscussionReplyResponse> rootReplies = new ArrayList<>();
        
        // 先转换所有回复
        for (DiscussionReply reply : allReplies) {
            DiscussionReplyResponse replyResponse = convertToReplyResponse(reply);
            replyResponse.setChildren(new ArrayList<>());
            replyMap.put(reply.getId(), replyResponse);
        }
        
        // 构建父子关系
        for (DiscussionReply reply : allReplies) {
            DiscussionReplyResponse replyResponse = replyMap.get(reply.getId());
            if (reply.getParentId() == null) {
                // 根回复
                rootReplies.add(replyResponse);
            } else {
                // 子回复
                DiscussionReplyResponse parentResponse = replyMap.get(reply.getParentId());
                if (parentResponse != null) {
                    parentResponse.getChildren().add(replyResponse);
                }
            }
        }
        
        return rootReplies;
    }
    
    @Override
    @Transactional
    public void likeTopic(Long topicId, Long userId) {
        log.info("点赞讨论主题，主题ID: {}, 用户ID: {}", topicId, userId);

        DiscussionTopic topic = topicMapper.selectById(topicId);
        if (topic == null) {
            throw new RuntimeException("讨论主题不存在");
        }

        // 检查用户是否已经点赞过
        int likedCount = topicLikeMapper.checkUserLiked(topicId, userId);
        if (likedCount > 0) {
            throw new RuntimeException("您已经点赞过此主题");
        }

        // 创建点赞记录
        TopicLike topicLike = new TopicLike(topicId, userId);
        topicLikeMapper.insert(topicLike);

        // 增加主题的点赞数量
        topicMapper.incrementLikeCount(topicId);
    }

    @Override
    @Transactional
    public void unlikeTopic(Long topicId, Long userId) {
        log.info("取消点赞讨论主题，主题ID: {}, 用户ID: {}", topicId, userId);

        DiscussionTopic topic = topicMapper.selectById(topicId);
        if (topic == null) {
            throw new RuntimeException("讨论主题不存在");
        }

        // 检查用户是否已经点赞过
        int likedCount = topicLikeMapper.checkUserLiked(topicId, userId);
        if (likedCount == 0) {
            throw new RuntimeException("您还没有点赞过此主题");
        }

        // 删除点赞记录
        topicLikeMapper.deleteUserLike(topicId, userId);

        // 减少主题的点赞数量
        topicMapper.decrementLikeCount(topicId);
    }

    @Override
    @Transactional
    public void likeReply(Long replyId, Long userId) {
        log.info("点赞回复，回复ID: {}, 用户ID: {}", replyId, userId);
        
        DiscussionReply reply = replyMapper.selectById(replyId);
        if (reply == null) {
            throw new RuntimeException("回复不存在");
        }
        
        // 检查用户是否已经点赞过
        int likedCount = replyLikeMapper.checkUserLiked(replyId, userId);
        if (likedCount > 0) {
            throw new RuntimeException("您已经点赞过此回复");
        }
        
        // 创建点赞记录
        ReplyLike replyLike = new ReplyLike(replyId, userId);
        replyLikeMapper.insert(replyLike);
        
        // 增加回复的点赞数量
        replyMapper.incrementLikeCount(replyId);
    }
    
    @Override
    @Transactional
    public void unlikeReply(Long replyId, Long userId) {
        log.info("取消点赞回复，回复ID: {}, 用户ID: {}", replyId, userId);
        
        DiscussionReply reply = replyMapper.selectById(replyId);
        if (reply == null) {
            throw new RuntimeException("回复不存在");
        }
        
        // 检查用户是否已经点赞过
        int likedCount = replyLikeMapper.checkUserLiked(replyId, userId);
        if (likedCount == 0) {
            throw new RuntimeException("您还没有点赞过此回复");
        }
        
        // 删除点赞记录
        replyLikeMapper.deleteUserLike(replyId, userId);
        
        // 减少回复的点赞数量
        replyMapper.decrementLikeCount(replyId);
    }
    
    @Override
    @Transactional
    public void deleteTopic(Long topicId, Long userId) {
        log.info("删除讨论主题，主题ID: {}, 用户ID: {}", topicId, userId);
        
        DiscussionTopic topic = topicMapper.selectById(topicId);
        if (topic == null) {
            throw new RuntimeException("讨论主题不存在");
        }
        
        // 检查权限：只有主题创建者可以删除
        if (!topic.getUserId().equals(userId)) {
            throw new RuntimeException("无权限删除此主题");
        }
        
        // 软删除：更新状态为HIDDEN
        topic.setStatus("HIDDEN");
        topicMapper.updateById(topic);
    }
    
    @Override
    @Transactional
    public void deleteReply(Long replyId, Long userId) {
        log.info("删除讨论回复，回复ID: {}, 用户ID: {}", replyId, userId);
        
        DiscussionReply reply = replyMapper.selectById(replyId);
        if (reply == null) {
            throw new RuntimeException("回复不存在");
        }
        
        // 检查权限：只有回复创建者可以删除
        if (!reply.getUserId().equals(userId)) {
            throw new RuntimeException("无权限删除此回复");
        }
        
        // 软删除：更新状态为HIDDEN
        reply.setStatus("HIDDEN");
        replyMapper.updateById(reply);
        
        // 更新主题回复数量
        topicMapper.decrementReplyCount(reply.getTopicId());
    }
    
    @Override
    @Transactional
    public void pinTopic(Long topicId, Long userId) {
        log.info("置顶讨论主题，主题ID: {}, 用户ID: {}", topicId, userId);
        
        DiscussionTopic topic = topicMapper.selectById(topicId);
        if (topic == null) {
            throw new RuntimeException("讨论主题不存在");
        }
        
        // TODO: 这里应该检查用户权限，只有教师或管理员可以置顶
        // 暂时允许所有用户置顶
        
        topic.setIsPinned(true);
        topic.setUpdatedAt(java.time.LocalDateTime.now());
        topicMapper.updateById(topic);
    }
    
    @Override
    @Transactional
    public void unpinTopic(Long topicId, Long userId) {
        log.info("取消置顶讨论主题，主题ID: {}, 用户ID: {}", topicId, userId);
        
        DiscussionTopic topic = topicMapper.selectById(topicId);
        if (topic == null) {
            throw new RuntimeException("讨论主题不存在");
        }
        
        // TODO: 这里应该检查用户权限，只有教师或管理员可以取消置顶
        // 暂时允许所有用户取消置顶
        
        topic.setIsPinned(false);
        topic.setUpdatedAt(java.time.LocalDateTime.now());
        topicMapper.updateById(topic);
    }
    
    /**
     * 转换主题实体为响应DTO
     */
    private DiscussionTopicResponse convertToTopicResponse(DiscussionTopic topic) {
        DiscussionTopicResponse response = new DiscussionTopicResponse();
        BeanUtils.copyProperties(topic, response);
        
        // 获取用户信息
        try {
            Result<UserServiceClient.UserBasicInfo> userResult = userServiceClient.getUserBasicInfo(topic.getUserId());
            if (userResult != null && userResult.isSuccess() && userResult.getData() != null) {
                UserServiceClient.UserBasicInfo userInfo = userResult.getData();
                response.setUsername(userInfo.getUsername());
                response.setNickname(userInfo.getNickname());
                response.setAvatarUrl(userInfo.getAvatarUrl());
                response.setUserRole(userInfo.getRole());
            } else {
                log.warn("获取用户信息失败，用户ID: {}, 结果: {}", topic.getUserId(), userResult);
                response.setUsername("未知用户");
                response.setNickname("未知用户");
                response.setAvatarUrl(null);
                response.setUserRole("STUDENT");
            }
        } catch (Exception e) {
            log.error("调用用户服务失败，用户ID: {}, 错误: {}", topic.getUserId(), e.getMessage(), e);
            response.setUsername("未知用户");
            response.setNickname("未知用户");
            response.setAvatarUrl(null);
            response.setUserRole("STUDENT");
        }
        
        return response;
    }
    
    /**
     * 转换回复实体为响应DTO
     */
    private DiscussionReplyResponse convertToReplyResponse(DiscussionReply reply) {
        DiscussionReplyResponse response = new DiscussionReplyResponse();
        BeanUtils.copyProperties(reply, response);
        
        // 获取用户信息
        try {
            Result<UserServiceClient.UserBasicInfo> userResult = userServiceClient.getUserBasicInfo(reply.getUserId());
            if (userResult != null && userResult.isSuccess() && userResult.getData() != null) {
                UserServiceClient.UserBasicInfo userInfo = userResult.getData();
                response.setUsername(userInfo.getUsername());
                response.setNickname(userInfo.getNickname());
                response.setAvatarUrl(userInfo.getAvatarUrl());
                response.setUserRole(userInfo.getRole());
            } else {
                log.warn("获取用户信息失败，用户ID: {}, 结果: {}", reply.getUserId(), userResult);
                response.setUsername("未知用户");
                response.setNickname("未知用户");
                response.setAvatarUrl(null);
                response.setUserRole("STUDENT");
            }
        } catch (Exception e) {
            log.error("调用用户服务失败，用户ID: {}, 错误: {}", reply.getUserId(), e.getMessage(), e);
            response.setUsername("未知用户");
            response.setNickname("未知用户");
            response.setAvatarUrl(null);
            response.setUserRole("STUDENT");
        }
        
        return response;
    }
}