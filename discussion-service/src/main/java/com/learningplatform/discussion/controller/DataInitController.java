package com.learningplatform.discussion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据初始化控制器
 */
@RestController
@RequestMapping("/api/init")
public class DataInitController {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @PostMapping("/test-data")
    public Map<String, Object> initTestData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 临时禁用外键检查
            jdbcTemplate.execute("SET FOREIGN_KEY_CHECKS = 0");
            
            // 创建测试用户数据
            String insertUser = "INSERT INTO users (id, username, email, password, nickname, role, status, created_at, updated_at) " +
                    "VALUES (1, 'testuser', '<EMAIL>', 'password123', '测试用户', 'STUDENT', 'ACTIVE', NOW(), NOW()) " +
                    "ON DUPLICATE KEY UPDATE username = VALUES(username)";
            int userRows = jdbcTemplate.update(insertUser);
            
            // 创建测试课程分类
            String insertCategory = "INSERT INTO course_categories (id, name, description, sort_order, status, created_at, updated_at) " +
                    "VALUES (1, '编程开发', '编程开发相关课程', 1, 'ACTIVE', NOW(), NOW()) " +
                    "ON DUPLICATE KEY UPDATE name = VALUES(name)";
            int categoryRows = jdbcTemplate.update(insertCategory);
            
            // 创建测试课程数据
            String insertCourse = "INSERT INTO courses (id, title, description, teacher_id, category_id, price, duration, status, created_at, updated_at) " +
                    "VALUES (1, 'Spring Boot 入门教程', '从零开始学习Spring Boot框架', 1, 1, 99.00, 120, 'PUBLISHED', NOW(), NOW()) " +
                    "ON DUPLICATE KEY UPDATE title = VALUES(title)";
            int courseRows = jdbcTemplate.update(insertCourse);
            
            // 重新启用外键检查
            jdbcTemplate.execute("SET FOREIGN_KEY_CHECKS = 1");
            
            result.put("success", true);
            result.put("message", "测试数据初始化成功");
            result.put("userRows", userRows);
            result.put("categoryRows", categoryRows);
            result.put("courseRows", courseRows);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "初始化失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }
}