package com.learningplatform.discussion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试数据控制器
 */
@RestController
@RequestMapping("/api/test")
public class TestDataController {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @PostMapping("/init-data")
    public Map<String, Object> initTestData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 先创建courses表（如果不存在）
            String createCoursesTable = "CREATE TABLE IF NOT EXISTS courses (" +
                    "id BIGINT PRIMARY KEY AUTO_INCREMENT, " +
                    "title VARCHAR(255) NOT NULL, " +
                    "description TEXT, " +
                    "instructor_id BIGINT, " +
                    "category_id BIGINT, " +
                    "price DECIMAL(10,2), " +
                    "duration INT, " +
                    "status VARCHAR(50), " +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" +
                    ")";
            
            jdbcTemplate.execute(createCoursesTable);
            
            // 先创建users表（如果不存在）
            String createUsersTable = "CREATE TABLE IF NOT EXISTS users (" +
                    "id BIGINT PRIMARY KEY AUTO_INCREMENT, " +
                    "username VARCHAR(255) NOT NULL, " +
                    "email VARCHAR(255) NOT NULL, " +
                    "password VARCHAR(255) NOT NULL, " +
                    "role VARCHAR(50), " +
                    "status VARCHAR(50), " +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" +
                    ")";
            
            jdbcTemplate.execute(createUsersTable);
            
            // 插入测试课程数据
            int courseRows = 0;
            try {
                String insertCourse = "INSERT INTO courses (id, title, description, instructor_id, category_id, price, duration, status, created_at, updated_at) " +
                        "VALUES (1, 'Spring Boot 入门教程', '从零开始学习Spring Boot框架', 1, 1, 99.00, 120, 'PUBLISHED', NOW(), NOW())";
                courseRows = jdbcTemplate.update(insertCourse);
            } catch (Exception e) {
                // 如果已存在则忽略
            }
            
            // 插入测试用户数据
            int userRows = 0;
            try {
                String insertUser = "INSERT INTO users (id, username, email, password, role, status, created_at, updated_at) " +
                        "VALUES (1, 'testuser', '<EMAIL>', 'password123', 'STUDENT', 'ACTIVE', NOW(), NOW())";
                userRows = jdbcTemplate.update(insertUser);
            } catch (Exception e) {
                // 如果已存在则忽略
            }
            
            result.put("success", true);
            result.put("message", "测试数据初始化成功");
            result.put("courseRows", courseRows);
            result.put("userRows", userRows);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "测试数据初始化失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @PostMapping("/disable-foreign-key")
    public Map<String, Object> disableForeignKey() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 暂时禁用外键检查
            jdbcTemplate.execute("SET FOREIGN_KEY_CHECKS = 0");
            
            // 插入测试课程数据（强制插入）
            String insertCourse = "INSERT INTO courses (id, title, description, instructor_id, category_id, price, duration, status, created_at, updated_at) " +
                    "VALUES (1, 'Spring Boot 入门教程', '从零开始学习Spring Boot框架', 1, 1, 99.00, 120, 'PUBLISHED', NOW(), NOW()) " +
                    "ON DUPLICATE KEY UPDATE title = VALUES(title)";
            
            int courseRows = jdbcTemplate.update(insertCourse);
            
            // 重新启用外键检查
            jdbcTemplate.execute("SET FOREIGN_KEY_CHECKS = 1");
            
            result.put("success", true);
            result.put("message", "外键检查已处理，课程数据已插入");
            result.put("courseRows", courseRows);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "处理外键失败: " + e.getMessage());
        }
        
        return result;
    }
}