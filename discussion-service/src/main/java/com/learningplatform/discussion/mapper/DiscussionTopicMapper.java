package com.learningplatform.discussion.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.learningplatform.discussion.entity.DiscussionTopic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 讨论主题Mapper接口
 */
@Mapper
public interface DiscussionTopicMapper extends BaseMapper<DiscussionTopic> {
    
    /**
     * 根据课程ID分页查询讨论主题
     */
    @Select("SELECT * FROM discussion_topics WHERE course_id = #{courseId} AND status = 'ACTIVE' " +
            "ORDER BY is_pinned DESC, created_at DESC")
    IPage<DiscussionTopic> selectTopicsByCourseId(Page<DiscussionTopic> page, @Param("courseId") Long courseId);
    
    /**
     * 搜索讨论主题
     */
    @Select("SELECT * FROM discussion_topics WHERE course_id = #{courseId} AND status = 'ACTIVE' " +
            "AND (title LIKE CONCAT('%', #{keyword}, '%') OR content LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY is_pinned DESC, created_at DESC")
    IPage<DiscussionTopic> searchTopics(Page<DiscussionTopic> page, 
                                       @Param("courseId") Long courseId, 
                                       @Param("keyword") String keyword);
    
    /**
     * 增加查看次数
     */
    @Update("UPDATE discussion_topics SET view_count = view_count + 1 WHERE id = #{id}")
    int incrementViewCount(@Param("id") Long id);
    
    /**
     * 增加回复数量
     */
    @Update("UPDATE discussion_topics SET reply_count = reply_count + 1 WHERE id = #{id}")
    int incrementReplyCount(@Param("id") Long id);
    
    /**
     * 减少回复数量
     */
    @Update("UPDATE discussion_topics SET reply_count = reply_count - 1 WHERE id = #{id} AND reply_count > 0")
    int decrementReplyCount(@Param("id") Long id);
    
    /**
     * 获取热门讨论主题（分页）
     */
    @Select("SELECT * FROM discussion_topics WHERE course_id = #{courseId} AND status = 'ACTIVE' " +
            "ORDER BY (reply_count * 2 + view_count) DESC, created_at DESC")
    IPage<DiscussionTopic> selectHotTopics(Page<DiscussionTopic> page, @Param("courseId") Long courseId);
    
    /**
     * 按热度排序获取课程讨论主题
     */
    @Select("SELECT * FROM discussion_topics WHERE course_id = #{courseId} AND status = 'ACTIVE' " +
            "ORDER BY is_pinned DESC, (reply_count * 2 + view_count) DESC, created_at DESC")
    IPage<DiscussionTopic> selectTopicsByCourseIdOrderByHot(Page<DiscussionTopic> page, @Param("courseId") Long courseId);
    
    /**
     * 按回复数排序获取课程讨论主题
     */
    @Select("SELECT * FROM discussion_topics WHERE course_id = #{courseId} AND status = 'ACTIVE' " +
            "ORDER BY is_pinned DESC, reply_count DESC, created_at DESC")
    IPage<DiscussionTopic> selectTopicsByCourseIdOrderByReplies(Page<DiscussionTopic> page, @Param("courseId") Long courseId);
    
    /**
     * 按查看数排序获取课程讨论主题
     */
    @Select("SELECT * FROM discussion_topics WHERE course_id = #{courseId} AND status = 'ACTIVE' " +
            "ORDER BY is_pinned DESC, view_count DESC, created_at DESC")
    IPage<DiscussionTopic> selectTopicsByCourseIdOrderByViews(Page<DiscussionTopic> page, @Param("courseId") Long courseId);

    /**
     * 增加点赞数量
     */
    @Update("UPDATE discussion_topics SET like_count = like_count + 1 WHERE id = #{id}")
    int incrementLikeCount(@Param("id") Long id);

    /**
     * 减少点赞数量
     */
    @Update("UPDATE discussion_topics SET like_count = like_count - 1 WHERE id = #{id} AND like_count > 0")
    int decrementLikeCount(@Param("id") Long id);
}