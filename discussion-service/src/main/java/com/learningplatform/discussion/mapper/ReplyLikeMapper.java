package com.learningplatform.discussion.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.learningplatform.discussion.entity.ReplyLike;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 回复点赞记录Mapper接口
 */
@Mapper
public interface ReplyLikeMapper extends BaseMapper<ReplyLike> {
    
    /**
     * 检查用户是否已经点赞过回复
     */
    @Select("SELECT COUNT(*) FROM reply_likes WHERE reply_id = #{replyId} AND user_id = #{userId}")
    int checkUserLiked(@Param("replyId") Long replyId, @Param("userId") Long userId);
    
    /**
     * 删除用户对回复的点赞记录
     */
    @Delete("DELETE FROM reply_likes WHERE reply_id = #{replyId} AND user_id = #{userId}")
    int deleteUserLike(@Param("replyId") Long replyId, @Param("userId") Long userId);
    
    /**
     * 获取回复的点赞数量
     */
    @Select("SELECT COUNT(*) FROM reply_likes WHERE reply_id = #{replyId}")
    int getLikeCount(@Param("replyId") Long replyId);
}