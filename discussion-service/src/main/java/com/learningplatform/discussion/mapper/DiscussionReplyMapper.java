package com.learningplatform.discussion.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.learningplatform.discussion.entity.DiscussionReply;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 讨论回复Mapper接口
 */
@Mapper
public interface DiscussionReplyMapper extends BaseMapper<DiscussionReply> {
    
    /**
     * 根据主题ID分页查询回复
     */
    @Select("SELECT * FROM discussion_replies WHERE topic_id = #{topicId} AND status = 'ACTIVE' " +
            "ORDER BY created_at ASC")
    IPage<DiscussionReply> selectRepliesByTopicId(Page<DiscussionReply> page, @Param("topicId") Long topicId);

    /**
     * 根据主题ID分页查询回复（按点赞数排序）
     */
    @Select("SELECT * FROM discussion_replies WHERE topic_id = #{topicId} AND status = 'ACTIVE' " +
            "ORDER BY like_count DESC, created_at DESC")
    IPage<DiscussionReply> selectRepliesByTopicIdOrderByLikes(Page<DiscussionReply> page, @Param("topicId") Long topicId);
    
    /**
     * 根据父回复ID查询子回复
     */
    @Select("SELECT * FROM discussion_replies WHERE parent_id = #{parentId} AND status = 'ACTIVE' " +
            "ORDER BY created_at ASC")
    List<DiscussionReply> selectRepliesByParentId(@Param("parentId") Long parentId);
    
    /**
     * 获取主题的所有回复（包括嵌套回复）
     */
    @Select("SELECT * FROM discussion_replies WHERE topic_id = #{topicId} AND status = 'ACTIVE' " +
            "ORDER BY CASE WHEN parent_id IS NULL THEN id ELSE parent_id END, " +
            "CASE WHEN parent_id IS NULL THEN 0 ELSE 1 END, created_at ASC")
    List<DiscussionReply> selectAllRepliesByTopicId(@Param("topicId") Long topicId);
    
    /**
     * 增加点赞数
     */
    @Update("UPDATE discussion_replies SET like_count = like_count + 1 WHERE id = #{id}")
    int incrementLikeCount(@Param("id") Long id);
    
    /**
     * 减少点赞数
     */
    @Update("UPDATE discussion_replies SET like_count = like_count - 1 WHERE id = #{id} AND like_count > 0")
    int decrementLikeCount(@Param("id") Long id);
    
    /**
     * 统计主题回复数量
     */
    @Select("SELECT COUNT(*) FROM discussion_replies WHERE topic_id = #{topicId} AND status = 'ACTIVE'")
    int countRepliesByTopicId(@Param("topicId") Long topicId);
}