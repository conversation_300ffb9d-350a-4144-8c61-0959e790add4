package com.learningplatform.discussion.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.learningplatform.discussion.entity.TopicLike;
import org.apache.ibatis.annotations.*;

/**
 * 主题点赞记录Mapper接口
 */
@Mapper
public interface TopicLikeMapper extends BaseMapper<TopicLike> {
    
    /**
     * 检查用户是否已经点赞过主题
     */
    @Select("SELECT COUNT(*) FROM topic_likes WHERE topic_id = #{topicId} AND user_id = #{userId}")
    int checkUserLiked(@Param("topicId") Long topicId, @Param("userId") Long userId);
    
    /**
     * 删除用户对主题的点赞记录
     */
    @Delete("DELETE FROM topic_likes WHERE topic_id = #{topicId} AND user_id = #{userId}")
    int deleteUserLike(@Param("topicId") Long topicId, @Param("userId") Long userId);
    
    /**
     * 获取主题的点赞数量
     */
    @Select("SELECT COUNT(*) FROM topic_likes WHERE topic_id = #{topicId}")
    int getLikeCount(@Param("topicId") Long topicId);
}
