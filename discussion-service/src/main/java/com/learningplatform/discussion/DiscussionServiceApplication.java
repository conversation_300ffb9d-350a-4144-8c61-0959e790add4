package com.learningplatform.discussion;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 讨论服务启动类
 */
@SpringBootApplication
@EnableDiscoveryClient
@MapperScan("com.learningplatform.discussion.mapper")
@EnableFeignClients
public class DiscussionServiceApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(DiscussionServiceApplication.class, args);
    }
}