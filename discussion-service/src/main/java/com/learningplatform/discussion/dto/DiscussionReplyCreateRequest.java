package com.learningplatform.discussion.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 创建讨论回复请求DTO
 */
public class DiscussionReplyCreateRequest {
    
    /**
     * 主题ID
     */
    @NotNull(message = "主题ID不能为空")
    private Long topicId;
    
    /**
     * 父回复ID（可选，用于嵌套回复）
     */
    private Long parentId;
    
    /**
     * 回复内容
     */
    @NotBlank(message = "回复内容不能为空")
    private String content;
    
    // Getters and Setters
    public Long getTopicId() {
        return topicId;
    }
    
    public void setTopicId(Long topicId) {
        this.topicId = topicId;
    }
    
    public Long getParentId() {
        return parentId;
    }
    
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
}