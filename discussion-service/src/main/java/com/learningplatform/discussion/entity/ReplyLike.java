package com.learningplatform.discussion.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

/**
 * 回复点赞记录实体
 */
@TableName("reply_likes")
public class ReplyLike {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long replyId;
    
    private Long userId;
    
    private LocalDateTime createdAt;
    
    // 构造函数
    public ReplyLike() {}
    
    public ReplyLike(Long replyId, Long userId) {
        this.replyId = replyId;
        this.userId = userId;
        this.createdAt = LocalDateTime.now();
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getReplyId() {
        return replyId;
    }
    
    public void setReplyId(Long replyId) {
        this.replyId = replyId;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "ReplyLike{" +
                "id=" + id +
                ", replyId=" + replyId +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                '}';
    }
}