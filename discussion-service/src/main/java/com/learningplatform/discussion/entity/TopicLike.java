package com.learningplatform.discussion.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

/**
 * 主题点赞记录实体
 */
@TableName("topic_likes")
public class TopicLike {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long topicId;
    
    private Long userId;
    
    private LocalDateTime createdAt;
    
    // 构造函数
    public TopicLike() {}
    
    public TopicLike(Long topicId, Long userId) {
        this.topicId = topicId;
        this.userId = userId;
        this.createdAt = LocalDateTime.now();
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getTopicId() {
        return topicId;
    }
    
    public void setTopicId(Long topicId) {
        this.topicId = topicId;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "TopicLike{" +
                "id=" + id +
                ", topicId=" + topicId +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                '}';
    }
}
