# 在线学习平台 - 基于Spring Cloud Alibaba的微服务架构

## 项目概述
基于Spring Cloud Alibaba构建的在线学习平台，采用微服务架构设计，支持课程管理、用户学习、智能推荐等功能。

## 技术栈
- **Java**: 22
- **Spring Boot**: 3.2.0
- **Spring Cloud**: 2023.0.0
- **Spring Cloud Alibaba**: 2022.0.0.0
- **数据库**: MySQL 8.4.6
- **ORM**: MyBatis Plus 3.5.4
- **服务注册中心**: Nacos 2.3.0
- **API网关**: Spring Cloud Gateway
- **认证**: JWT

## 项目结构
```
online-learning-platform/
├── pom.xml                     # 父项目POM文件
├── common/                     # 公共模块
│   └── src/main/java/com/learningplatform/common/
│       └── response/Result.java # 统一响应结果类
├── gateway-service/            # API网关服务
├── user-service/              # 用户服务
├── course-service/            # 课程服务
├── learning-service/          # 学习服务
├── recommendation-service/    # 推荐服务
├── discussion-service/        # 讨论服务
└── infrastructure/            # 基础设施
    ├── nacos/                 # Nacos服务注册中心
    │   ├── nacos/            # Nacos服务器
    │   └── start-nacos.sh    # Nacos启动脚本
    └── database/
        └── init.sql          # 数据库初始化脚本
```

## 微服务模块说明

### 1. Gateway Service (网关服务) - 端口: 8080
- 统一入口，路由转发
- 负载均衡
- 跨域处理
- 认证鉴权

### 2. User Service (用户服务) - 端口: 8081
- 用户注册、登录
- 用户信息管理
- 权限管理

### 3. Course Service (课程服务) - 端口: 8082
- 课程管理
- 课程分类
- 课程内容管理

### 4. Learning Service (学习服务) - 端口: 8083
- 学习进度跟踪
- 学习记录管理
- 学习统计

### 5. Recommendation Service (推荐服务) - 端口: 8084
- 个性化课程推荐
- 学习路径推荐

### 6. Discussion Service (讨论服务) - 端口: 8085
- 课程讨论
- 问答互动

## 数据库设计

### 核心表结构
- `users`: 用户表
- `courses`: 课程表
- `course_categories`: 课程分类表
- `course_chapters`: 课程章节表
- `course_videos`: 课程视频表
- `student_courses`: 学生选课表
- `learning_records`: 学习记录表
- `course_reviews`: 课程评价表
- `discussion_topics`: 讨论主题表
- `discussion_replies`: 讨论回复表

## 快速开始

### 1. 环境准备
- Java 22
- Maven 3.6+
- MySQL 8.4.6
- jenv (Java版本管理)

### 2. 数据库初始化
```bash
# 创建数据库
mysql -h localhost -u study250801 -p'@yw@%K!@3^Dm' -e "CREATE DATABASE learning_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入表结构和初始数据
mysql -h localhost -u study250801 -p'@yw@%K!@3^Dm' learning_platform < infrastructure/database/init.sql
```

### 3. 启动Nacos服务注册中心
```bash
./infrastructure/nacos/start-nacos.sh
```
访问地址: http://localhost:8848/nacos
默认用户名/密码: nacos/nacos

### 4. 编译项目
```bash
mvn clean compile
```

### 5. 启动各个微服务
```bash
# 启动网关服务
cd gateway-service && mvn spring-boot:run

# 启动用户服务
cd user-service && mvn spring-boot:run

# 其他服务类似...
```

## 开发规范

### 1. 代码结构
每个微服务遵循标准的Spring Boot项目结构：
```
src/main/java/com/learningplatform/{service}/
├── controller/     # 控制器层
├── service/        # 业务逻辑层
├── mapper/         # 数据访问层
├── entity/         # 实体类
├── dto/           # 数据传输对象
└── config/        # 配置类
```

### 2. 统一响应格式
使用 `Result<T>` 类作为统一响应格式：
```java
// 成功响应
Result.success(data);

// 错误响应
Result.error("错误信息");
```

### 3. 服务间通信
使用OpenFeign进行服务间调用，统一在各服务的feign包下定义客户端接口。

## 配置说明

### Nacos配置
- 服务注册地址: localhost:8848
- 命名空间: public
- 数据源: MySQL (nacos_config数据库)

### 数据库配置
- 主机: localhost:3306
- 数据库: learning_platform
- 用户名: study250801
- 字符集: utf8mb4

## 下一步开发计划
1. 完善各微服务的业务逻辑实现
2. 添加服务间调用的Feign客户端
3. 实现JWT认证和授权
4. 添加Redis缓存支持
5. 完善异常处理和日志记录
6. 添加单元测试和集成测试