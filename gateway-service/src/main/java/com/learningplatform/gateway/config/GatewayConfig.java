package com.learningplatform.gateway.config;

import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 网关路由配置
 */
@Configuration
public class GatewayConfig {

    /**
     * 自定义路由配置
     * 这里可以添加更复杂的路由规则，目前主要依赖application.yml配置
     */
    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
            // 可以在这里添加更复杂的路由规则
            // 例如：基于请求头、参数等的路由
            .build();
    }
}