package com.learningplatform.gateway.filter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.learningplatform.common.response.Result;
import com.learningplatform.gateway.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * JWT认证过滤器
 */
@Component
public class JwtAuthenticationFilter implements GlobalFilter, Ordered {

    @Autowired
    private JwtUtil jwtUtil;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 不需要认证的路径
    private static final List<String> EXCLUDE_PATHS = Arrays.asList(
        "/api/user/register",
        "/api/user/login",
        "/api/user/verify-email",
        "/api/course/list",
        "/api/course/search",
        "/actuator"
    );

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();
        String method = request.getMethod().name();

        // 对于OPTIONS请求（CORS预检），直接放行
        if ("OPTIONS".equals(method)) {
            return chain.filter(exchange);
        }

        // 检查是否为不需要认证的路径
        if (isExcludePath(path)) {
            return chain.filter(exchange);
        }

        // 获取Authorization头和X-User-Id头
        String authHeader = request.getHeaders().getFirst("Authorization");
        String userIdHeader = request.getHeaders().getFirst("X-User-Id");

        // 如果没有Authorization头但有X-User-Id头，允许通过（用于测试）
        if ((!StringUtils.hasText(authHeader) || !authHeader.startsWith("Bearer ")) && StringUtils.hasText(userIdHeader)) {
            // 直接传递X-User-Id头，不做JWT验证
            return chain.filter(exchange);
        }

        if (!StringUtils.hasText(authHeader) || !authHeader.startsWith("Bearer ")) {
            return handleUnauthorized(exchange, "缺少认证令牌");
        }

        // 提取JWT令牌
        String token = authHeader.substring(7);
        
        // 验证JWT令牌
        if (!jwtUtil.validateToken(token)) {
            return handleUnauthorized(exchange, "无效的认证令牌");
        }

        // 检查令牌是否过期
        if (jwtUtil.isTokenExpired(token)) {
            return handleUnauthorized(exchange, "认证令牌已过期");
        }

        // 从令牌中获取用户信息并添加到请求头
        String username = jwtUtil.getUsernameFromToken(token);
        String userId = jwtUtil.getUserIdFromToken(token);
        String role = jwtUtil.getRoleFromToken(token);

        if (username != null && userId != null) {
            ServerHttpRequest modifiedRequest = request.mutate()
                .header("X-User-Id", userId)
                .header("X-Username", username)
                .header("X-User-Role", role != null ? role : "STUDENT")
                .build();

            exchange = exchange.mutate().request(modifiedRequest).build();
        }

        return chain.filter(exchange);
    }

    /**
     * 检查路径是否需要排除认证
     */
    private boolean isExcludePath(String path) {
        return EXCLUDE_PATHS.stream().anyMatch(path::startsWith);
    }

    /**
     * 处理未授权请求
     */
    private Mono<Void> handleUnauthorized(ServerWebExchange exchange, String message) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);

        Result<Object> result = Result.error("401", message);
        
        try {
            String body = objectMapper.writeValueAsString(result);
            DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        } catch (JsonProcessingException e) {
            DataBuffer buffer = response.bufferFactory().wrap("认证失败".getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        }
    }

    @Override
    public int getOrder() {
        return 1; // 在CORS过滤器之后执行
    }
}